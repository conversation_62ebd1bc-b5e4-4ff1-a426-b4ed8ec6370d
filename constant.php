<?php

define("ERROR_NOERROR", 0);
define("ERROR_DATABASE", 1);
define("ERROR_PARA", 2);
define("ERROR_REGISTER", 3);

define("VERIFY_FAIL", 0);
define("VERIFY_OK", 1);
define("VERIFY_FIRST", 2);

define("DEVICE_TYPE_PHONE", 0x01);
define("VERIFY_TYPE_TABLET", 0x02);
define("VERIFY_TYPE_TV", 0x04);
define("VERIFY_TYPE_CAR", 0x08);
define('NETTYPEARRAY', array(0, 1, 2, 3, 4, 5, 6));

$VS_KEY = 'dK6iD7kY7zY2oN5c';
$PICO_KEY = 0x69a2d51b;
$LOGIN_TOKEN_KEY = 'hR7lP4tN4xE8lO6a';

$LOGINTOKENLEN = 40;

$EMAILCODEVALIDMINS = 15; //mins
$EMAILCODEERRORCNT = 5;
$CODEERRORLIMITSECS = 15 * 60; //secs

$LOGIN_EVENT_VERSIONAPI = 1;
$LOGIN_EVENT_GAMEHALLAPI = 2;
$LOGIN_EVENT_RANKAPI = 3;

$AES128_KEY = 'cH7nI2oM7xB1oF8k';
$EMAILAES128KEY = 'hG7cX9fF9jK0rU0y';

$DEVICE_REG_LIMIT = 999;
$DEVICE_FINGER_REG_LIMIT = 10;

$SEX_MALE = 1;
$SEX_FEMALE = 2;
$SEX_OTHER = 3;

//virtual goods type
$GOODS_TYPE_GAME = 0;
$GOODS_TYPE_TX = 1;
$GOODS_TYPE_TXK = 2;
$GOODS_TYPE_BQ = 3;
$GOODS_TYPE_PROP = 4;

$BUY_DIAMOND_TYPE = 0;
$BUY_MEMBER_TYPE = 1;

$PAYPAL_TYPE = 0;
$VISA_TYPE = 1;

$TYPE_BETA_UPDATE = 0;
$TYPE_ALL_UPDATE = 1;

$GLOBAL_MATCH_APP = 0;
$YZKOFVS_APP = 1;
$VSWXJJ_APP = 2;
$VSWXMINIJJ_APP = 3;

$SERVER_REGION_IDX_CHINA = 0;
$SERVER_REGION_IDX_ASIA = 1;
$SERVER_REGION_IDX_EUROPE = 2;
$SERVER_REGION_IDX_AMERICA = 3;
$SERVER_REGION_IDX_HK = 4;
$SERVER_REGION_IDX_ME = 5;
$SERVER_REGION_IDX_CHINA_MAINLAND = 6;
$SERVER_REGION_IDX_RUSSIA = 7;
$SERVER_REGION_IDX_BR = 8;
$SERVER_REGION_IDX_CHINA2 = 9;

// $SERVER_REGION_CUR_IDX = $SERVER_REGION_IDX_CHINA;


$SERVER_REGION_NAME_ARRAY = array(
    "china", //0
    "asia", //1
    "europe", //2
    "america", //3
    "hk", //4
    "me", //5
    "china(MAINLAND)", //6
    "russia", //7
    "brazil", // 8
    "china2" // 9
);

$SERVER_USER_IDX = array(
    $SERVER_REGION_IDX_ASIA => 300000000,
    $SERVER_REGION_IDX_EUROPE => 400000000,
    $SERVER_REGION_IDX_AMERICA => 200000000,
    $SERVER_REGION_IDX_HK => 100000000,
    $SERVER_REGION_IDX_ME => 500000000,
    $SERVER_REGION_IDX_RUSSIA => 600000000,
    $SERVER_REGION_IDX_CHINA_MAINLAND => 50000000,
    $SERVER_REGION_IDX_BR => 700000000,
    $SERVER_REGION_IDX_CHINA2 => 80000000
);

$SERVER_RES_PREFIX = array(
    "https://gm-res-hk.oss-cn-hongkong.aliyuncs.com/", //0
    "https://gm-res-as.oss-ap-southeast-5.aliyuncs.com/", //1
    "https://gm-res-eu.oss-eu-central-1.aliyuncs.com/", //2
    "https://gm-res-us.oss-us-east-1.aliyuncs.com/", //3
    "https://gm-res-hk.oss-cn-hongkong.aliyuncs.com/", //4
    "https://gm-res-me.oss-me-east-1.aliyuncs.com/", //5
    "https://gm-res-cn.oss-cn-hangzhou.aliyuncs.com/", //6
    "https://gm-res-eu.oss-eu-central-1.aliyuncs.com/", //7
    "https://gm-res-us.oss-us-east-1.aliyuncs.com/", //8
    "https://gm-res-cn.oss-cn-hangzhou.aliyuncs.com/", //9
    // "http://gm-res-ru.gmarcade.co/", //7
);
$YZVS_GAME_ICON_PREFIX  = "gameicon/";
$YZVS_ROM_ICON_PREFIX  = "roms/";

$GAME_TYPE_ARRAY = array(
    "fighting",
    "coop",
    "shoot",
    "sports",
);

$DEV_CORE_TYPE_ARRAY = array(
    1 => "海思",
    2 => "S905"
);

/*--- game rank level [START] ---*/
const gMaxGameLevel = 40;
const gMaxGameRankLevel = 7;
$gGameRankLevel = array(
    0 => 1,
    1 => 1,
    2 => 1,
    3 => 1,        //白色(Level没有0级，默认1级)
    4 => 2,
    5 => 2,
    6 => 2,
    7 => 2,        //绿色
    8 => 3,
    9 => 3,
    10 => 3,
    11 => 3,      //蓝色  
    12 => 4,
    13 => 4,
    14 => 4,
    15 => 4,    //紫色
    16 => 5,
    17 => 5,
    18 => 5,
    19 => 5,    //黄色
    20 => 6,
    21 => 6,
    22 => 6,
    23 => 6,    //红色
    24 => 6,
    25 => 6,
    26 => 6,
    27 => 6,    //红色
    28 => 7,
    29 => 7,
    30 => 7,
    31 => 7,    //橙色
    32 => 7,
    33 => 7,
    34 => 7,
    35 => 7,    //橙色
    36 => 7,
    37 => 7,
    38 => 7,
    39 => 7,    //橙色
    40 => 7                             //橙色
);
/*--- game rank level [END] ---*/

//vs match state [START]
const DB_P1_ESCAPE = 1;
const DB_P2_ESCAPE = 2;
const DB_P1_WIN = 3;
const DB_P2_WIN = 4;
const DB_PP_DRAW = 5;
//vs match state [END]

// const NETTYPEARRAY = [0, 1, 2, 3, 4, 5, 6];

/*--- PAY BUSINESS STATE [START] ---*/
const BUSINESS_STATE_FAIL = -1;
const BUSINESS_STATE_DEFAULT = 0;
const BUSINESS_STATE_FINISH = 100;

$gDLangTSuffix = 0; //Default Language Table Suffix
$gAllLanguage = array(
    0 => "en-US",
    1 => "zh-CN"
);

$gAllCurrency = array(
    "USD" => [0, "$"],
    // "CNY" => [1, "¥"],
    // "HKD" => [2, "HK$"],
    // "JPY" => [3, "¥"],
    // "KRW" => [4, "₩"],
    // "EUR" => [5, "€"],
    // "RUB" => [6, "₽"],
    // "GBP" => [7, "£"],
    // "TWD" => [8, "NT$"],
);

$OPENEXCHANGERATES_APPID = '********************************';
$EXCHANGERATE_BASE_CURRENCY = 'USD';

$NOT_AUTH_CHANNEL = array(
    500 => 1,
    501 => 1
);
