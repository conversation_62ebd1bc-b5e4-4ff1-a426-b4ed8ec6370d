<?php

include(dirname(__FILE__) . "/games_funs.php");

require_once './log.php';

//初始化日志
$logHandler = new CLogFileHandler("/tmp/chip_reg_yz_handgame_" . date('Y-m-d') . '.log');
$log = Log::Init($logHandler, 1);


class Response
{
    public $msg = 'ok';
    public $ret = 0;
};

$response = new Response();

$product_id = $_POST['device_id'];
$app_vcode = $_POST['app_vcode'];
$app_vname = $_POST['app_name'];
$token = $_POST['token'];

@$key_total = $_POST['key_total'];
@$key_ok = $_POST['key_ok'];
@$key_fail = $_POST['key_fail'];

if (NULL == $product_id || NULL == $token || 0 == strlen(trim($product_id))) {
    output_json_para_error($response);
    exit;
}
if (sha1($product_id . $VS_KEY) != $token) {
    output_json_token_error($response);
    exit;
}

if (NULL != $key_total) {
    $key_total = intval($key_total);
} else {
    $key_total = 0;
}
if (NULL != $key_ok) {
    $key_ok = intval($key_ok);
} else {
    $key_ok = 0;
}
if (NULL != $key_fail) {
    $key_fail = intval($key_fail);
} else {
    $key_fail = 0;
}

Log::DEBUG('CHIP ID:' . $product_id);

//connect database and query
$conn = db_connect();

if (!$conn) {
    output_json_db_error($response);
    exit;
}

$MYIP = get_ip();

$lFRegTime = NULL;
$lRegNum = 0;
$result = $conn->query("SELECT firstregtime,regnum FROM  t_hw_chip_list WHERE chipid='{$product_id}'");

if (!$result) {
    output_json_db_error($response);
    exit;
} else {

    $result_num = $result->num_rows;

    if ($result_num != 1) {
        Log::DEBUG('CHIP ID ERROR:' . $product_id);
        output_json_api_error($response, -2, 'chip id error!');
        exit;
    } else {
        $row = $result->fetch_assoc();

        if ($row) {
            $lFRegTime = $row['firstregtime'];
            $lRegNum = intval($row['regnum']);
            $lRegNum++;
            //update chip app info
            $UPDATEFREGTIME = '';
            if (NUll == $lFRegTime || 0 == strlen(trim($lFRegTime))) {
                $UPDATEFREGTIME = ',firstregtime=now() ';
            }
            $result_reg = $conn->query("UPDATE t_hw_chip_list SET key_total={$key_total},key_ok={$key_ok},key_fail={$key_fail},app_version='{$app_vcode}',app_version_name='{$app_vname}',lastregtime=now(),lastregip='{$MYIP}',regnum={$lRegNum}{$UPDATEFREGTIME} WHERE chipid='{$product_id}'");
            if (!$result_reg) {
                output_json_db_error($response);
                Log::DEBUG('CHIP REG ERROR:' . mysqli_error($conn));
                exit;
            }
        }
    }

    $result->free();
}

Log::DEBUG('TEST OK');

header('Content-type: application/json');
echo json_encode($response);

$conn->close();
