<?php

include(dirname(__FILE__) . "/games_funs.php");
//---------------- JSON DATA CLASS ---------------------------

class GLResponse
{
    public $msg = 'ok';
    public $ret = 0;

    public $icon_list = NULL;
};

class ICONINFO
{

    public $icon_id = 0;
    public $icon_url = '';
}

$response = new GLResponse();
//-----------------------------------------------------------

$product_id = $_GET['device_id'];
$token = $_GET['token'];

//-----------------------------------------------------------

if ($product_id == NULL || $token == NULL) {
    output_json_para_error($response);
    exit;
}

$product_id = trim($product_id);
$token = trim($token);

if (sha1($product_id . $VS_KEY) != $token) {
    output_json_token_error($response);
    exit;
}
//-----------------------------------------------------------

$gConn = db_connect();

if (!$gConn) {
    output_json_db_error($response);
    exit;
}

$product_id = mysqli_real_escape_string($gConn, $product_id);

if (!is_chip_id_exist($gConn, $response, $product_id)) {
    exit;
}

$result = $gConn->query("SELECT id,icon_url FROM t_hw_user_avatar_list WHERE is_show=1");
if (!$result) {
    output_json_db_error($response);
    exit;
} else {

    $result_num = $result->num_rows;
    for ($i = 0; $i < $result_num; $i++) {
        $row = $result->fetch_assoc();

        $lItem = new ICONINFO();
        $lItem->icon_id =  intval($row['id']);
        $lItem->icon_url =  $SERVER_RES_PREFIX[$SERVER_REGION_CUR_IDX] .  "avatar/" . trim($row['icon_url']);

        $response->icon_list[$i] = $lItem;
    }
    $result->free();
}


header('Content-type: application/json');
echo json_encode($response);

$gConn->close();
