<?php

include(dirname(__FILE__) . "/games_funs.php");

require_once(dirname(__FILE__) . "/GeoIP2/geoip2.phar");

use GeoIp2\Database\Reader;

//---------------- JSON DATA CLASS ---------------------------

class GLResponse
{

    public $msg = 'ok';
    public $ret = 0;

    public $server_region_idx = 0; //根据用户IP地址返回最佳的服务器 
};

$response = new GLResponse();

$product_id = $_GET['device_id'];
$token = $_GET['token'];

if (NULL == $product_id || NULL == $token || 0 == strlen(trim($product_id))) {
    output_json_para_error($response);
    exit;
}

$product_id = trim($product_id);
$token = trim($token);

if (sha1($product_id . $VS_KEY) != $token) {
    output_json_token_error($response);
    exit;
}


//connect database and query
$gConn = db_connect();

if (!$gConn) {
    output_json_db_error($response);
    exit;
}

$lMyIP = get_ip();
$lRegIPCode = "";
try {
    $reader = new Reader('./GeoIP2/GeoLite2-City.mmdb');
    $record = $reader->city($lMyIP);
    $lRegIPCode = trim($record->country->isoCode);
} catch (\Throwable $th) {
    //throw $th;
    $response->server_region_idx = $SERVER_REGION_CUR_IDX;
}

if (strlen($lRegIPCode) > 0) {
    $gResult = $gConn->query("SELECT id,server_region_idx FROM t_hw_country_list WHERE two_letter='{$lRegIPCode}'");
    if ($gResult) {
        $lNum = $gResult->num_rows;

        if (1 == $lNum) {
            $row = $gResult->fetch_assoc();
            $response->server_region_idx = intval($row['server_region_idx']);
        }
    }
}



header('Content-type: application/json');
echo json_encode($response);

$gConn->close();
