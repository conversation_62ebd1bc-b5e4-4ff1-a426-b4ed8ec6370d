<?php

include(dirname(__FILE__) . "/games_funs.php");

require_once(dirname(__FILE__) . "/GeoIP2/geoip2.phar");
require_once './log.php';

use GeoIp2\Database\Reader;

//初始化日志 windwos下路径为：C:\Users\<USER>\AppData\Local\Temp
$logHandler = new CLogFileHandler(sys_get_temp_dir() . DIRECTORY_SEPARATOR . "reg_yz_handgame_" . date('Y-m-d') . '.log');
$log = Log::Init($logHandler, 1);

//---------------- JSON DATA CLASS ---------------------------

class GLResponse
{

    public $msg = 'ok';
    public $ret = 0;

    public $user_id = 0; //用户ID

    public $name = ''; //账号（Email）
    public $nickname = ''; //昵称
    public $icon_url = ''; //头像URL
    public $gold = 0; //剩余金币

    public $server_region_idx = -1; //大区服务器ID(注册时选定，后期不能更改)
    public $abode_country = -1; //居住地国家ID(注册时选定，后期不能更改)
    //public $real_country = -1; //国籍
    public $region_id = -1; //区域ID

    public $is_new = 1;
    public $ptype = 1; //0->游客,1->正常用户,2->知名玩家,3->机器人    

    public $login_token = ''; //登录token

    public $cur_server_region_idx = -1; //当前大区服务器ID,无异常时server_region_idx与其相同，有异常则不同

};

$response = new GLResponse();
// $response->cur_server_region_idx = $SERVER_REGION_CUR_IDX;

$name = $_POST['name']; //email
$email_no = $_POST['email_no'];
$email_code = $_POST['email_code'];
$nickname = $_POST['nickname'];
$icon_id = $_POST['icon_id'];

// $server_region_idx = $_POST['server_region_idx'];
// $abode_country = $_POST['abode_country'];
$region_id = $_POST['region_id'];
$channel = $_POST['channel'];
// $real_country = $_POST['real_country'];

$product_id = $_POST['device_id'];

$dev_cpu = $_POST['dev_cpu'];
$dev_gpu = $_POST['dev_gpu'];
$dev_android = $_POST['dev_android'];
$dev_ram = $_POST['dev_ram'];
$dev_rom = $_POST['dev_rom'];
$dev_hdmi = $_POST['dev_hdmi'];
$dev_screen_w = $_POST['dev_screen_w'];
$dev_screen_h = $_POST['dev_screen_h'];


$token = $_POST['token'];

if (NULL == $name || NULL == $email_no || NULL == $email_code || NULL == $nickname || NULL === $icon_id || NULL === $region_id || NULL === $channel || NULL == $token || NULL == $product_id || NULL == $dev_cpu || NULL == $dev_gpu || NULL == $dev_android || NULL == $dev_ram || NULL == $dev_rom || NULL === $dev_hdmi || NULL == $dev_screen_w || NULL == $dev_screen_h) {
    output_json_para_error($response);
    exit;
}

$name = trim($name);
$email_no = trim($email_no);
$email_code = trim($email_code);
$nickname = trim($nickname);
$product_id = trim($product_id);
$icon_id = intval($icon_id);
$token = trim($token);

// $server_region_idx = intval($server_region_idx);
// $abode_country = intval($abode_country);
$region_id = intval($region_id);
$channel = intval($channel);
// $real_country = intval($real_country);

$dev_cpu = trim($dev_cpu);
$dev_gpu = trim($dev_gpu);
$dev_android = trim($dev_android);
$dev_ram = trim($dev_ram);
$dev_rom = trim($dev_rom);
$dev_hdmi = trim($dev_hdmi);
$dev_screen_w = trim($dev_screen_w);
$dev_screen_h = trim($dev_screen_h);

$icon_url = '';

if (0 == strlen($name) || 0 == strlen($email_no) || 0 == strlen($email_code) || 0 == strlen($nickname) || 0 == strlen($product_id)) {
    output_json_para_error($response);
    exit;
}

if (sha1($name . $email_no . $email_code . $product_id . $region_id . $channel . $VS_KEY) != $token) {
    output_json_token_error($response);
    exit;
}

// if ($SERVER_REGION_CUR_IDX != $server_region_idx) {
//     $response->server_region_idx = $server_region_idx;
//     $response->cur_server_region_idx = $SERVER_REGION_CUR_IDX;
//     output_json_api_error($response, -4, 'server region idx error!');
//     exit;
// }
if (!isValidEmailAddr($name)) {
    output_json_api_error($response, -3, 'It isn`t a valid Email!');
    exit;
}

if ($region_id == $SERVER_REGION_IDX_RUSSIA) {
    if (!isValidNickname_RU($nickname)) {
        output_json_api_error($response, -4, 'It isn`t a valid nickname!');
        exit;
    }
} else {
    if (!isValidNickname($nickname)) {
        output_json_api_error($response, -4, 'It isn`t a valid nickname!');
        exit;
    }
}

// if (strlen($dev_ram) > 64) {
//     $dev_ram = str_replace(["\u200e", "\u200f", "<200e>", "<200f>"], '', $dev_ram);
// }
// if (strlen($dev_rom) > 64) {
//     $dev_rom = str_replace(["\u200e", "\u200f", "<200e>", "<200f>"], '', $dev_rom);
// }

// 移除所有控制字符、不可见字符和 HTML 编码
$dev_ram = preg_replace('/[\x{200B}-\x{200F}\x{FEFF}\x{202A}-\x{202E}]+/u', '', $dev_ram);
$dev_rom = preg_replace('/[\x{200B}-\x{200F}\x{FEFF}\x{202A}-\x{202E}]+/u', '', $dev_rom);

// 移除可能的 HTML 编码形式（如 <200e>）
$dev_ram = preg_replace('/<200[eEfF]>/', '', $dev_ram);
$dev_rom = preg_replace('/<200[eEfF]>/', '', $dev_rom);


//connect database and query
$gConn = db_connect();

if (!$gConn) {
    output_json_db_error($response);
    exit;
}

$name = mysqli_real_escape_string($gConn, $name);
$email_no = mysqli_real_escape_string($gConn, $email_no);
$email_code = mysqli_real_escape_string($gConn, $email_code);
$product_id = mysqli_real_escape_string($gConn, $product_id);

$r_dev_regnum = 0;
$r_dev_cpu = '';
$r_dev_gpu = '';
$r_dev_android = '';
$r_dev_ram = '';
$r_dev_rom = '';
$r_dev_hdmi = '';
$r_dev_screen_w = '';
$r_dev_screen_h = '';

$real_macid = '';

$gResult = $gConn->query("SELECT macid,dev_regnum,dev_cpu,dev_gpu,dev_android,dev_ram,dev_rom,dev_hdmi,dev_screen_w,dev_screen_h FROM t_hw_chip_list WHERE macid_enc='{$product_id}'");

if (!$gResult) {
    output_json_db_error($response);
    exit;
} else {

    $result_num = $gResult->num_rows;

    if ($result_num < 1) {
        Log::DEBUG('CHIP ID ERROR:' . $product_id);
        output_json_api_error($response, -2, 'chip id error!');
        exit;
    } else {
        $row = $gResult->fetch_assoc();

        $real_macid = trim($row['macid']);
        $r_dev_regnum = intval($row['dev_regnum']);
        $r_dev_cpu = $row['dev_cpu'];
        $r_dev_gpu = $row['dev_gpu'];
        $r_dev_android = $row['dev_android'];
        $r_dev_ram = $row['dev_ram'];
        $r_dev_rom = $row['dev_rom'];
        $r_dev_hdmi = $row['dev_hdmi'];
        $r_dev_screen_w = $row['dev_screen_w'];
        $r_dev_screen_h = $row['dev_screen_h'];
    }
    $gResult->free();
}

$lAES = new AESSSL($EMAILAES128KEY);

$lEncName = $lAES->encrypt($name);

$lCMD = "SELECT user_id FROM t_all_gameusers WHERE name =  ? ";
$lStmt = $gConn->prepare($lCMD); //mysql client --> mysql server(network time)???
if (!$lStmt) {
    output_json_db_error($response);
    exit;
} else {
    $lStmt->bind_param("s", $lEncName);

    if (!$lStmt->execute()) { //mysql client --> mysql server(network time)???
        output_json_db_error($response);
        exit;
    } else {
        $lResult = $lStmt->get_result();

        $lNum = $lResult->num_rows;

        if ($lNum > 0) {
            output_json_api_error($response, -5, 'Email exist!');
            exit;
        }

        $lResult->free();
    }

    $lStmt->close();
}

$lCMD = "SELECT user_id FROM t_all_gameusers WHERE nickname =  ? ";
$lStmt = $gConn->prepare($lCMD); //mysql client --> mysql server(network time)???
if (!$lStmt) {
    output_json_db_error($response);
    exit;
} else {
    $lStmt->bind_param("s", $nickname);

    if (!$lStmt->execute()) { //mysql client --> mysql server(network time)???
        output_json_db_error($response);
        exit;
    } else {
        $lResult = $lStmt->get_result();

        $lNum = $lResult->num_rows;

        if ($lNum > 0) {
            output_json_api_error($response, -6, 'nickname exist!');
            exit;
        }

        $lResult->free();
    }

    $lStmt->close();
}

// $gResult = $gConn->query("SELECT id FROM t_hw_country_list WHERE id={$abode_country}");
// if (!$gResult) {
//     output_json_db_error($response);
//     exit;
// } else {
//     $lNum = $gResult->num_rows;

//     $isAbodeCExist = false;
//     // $isRealCExist = false;
//     for ($i = 0; $i < $lNum; $i++) {
//         $row = $gResult->fetch_assoc();
//         if ($abode_country == intval($row['id'])) {
//             $isAbodeCExist = true;
//         }
//         // if ($real_country == intval($row['id'])) {
//         //     $isRealCExist = true;
//         // }
//     }
//     if (!$isAbodeCExist) {
//         output_json_api_error($response, -7, 'country error!' . $abode_country);
//         exit;
//     }

//     $gResult->free();
// }

$gResult = $gConn->query("SELECT region_id FROM t_hw_all_server_region WHERE region_id={$region_id}");
if (!$gResult) {
    output_json_db_error($response);
    exit;
} else {
    $lNum = $gResult->num_rows;

    if ($lNum != 1) {
        output_json_api_error($response, -7, 'country error!' . $abode_country);
        exit;
    }

    $gResult->free();
}

$gResult = $gConn->query("SELECT channel FROM t_hw_all_channel WHERE channel={$channel}");
if (!$gResult) {
    output_json_db_error($response);
    exit;
} else {
    $lNum = $gResult->num_rows;

    if ($lNum != 1) {
        output_json_api_error($response, -13, 'channel error!');
        exit;
    }

    $gResult->free();
}


$lNowTime = date('Y-m-d H:i:s');
$lCMD = "SELECT email_no,email,code,state,channel,try_num,TIMESTAMPDIFF(MINUTE, `time`, '{$lNowTime}') AS mins FROM t_hw_email_verify_no WHERE email_no =  ? ";
$lStmt = $gConn->prepare($lCMD); //mysql client --> mysql server(network time)???
if (!$lStmt) {
    output_json_db_error($response);
    exit;
} else {
    $lStmt->bind_param("s", $email_no);

    if (!$lStmt->execute()) { //mysql client --> mysql server(network time)???
        output_json_db_error($response);
        exit;
    } else {
        $lResult = $lStmt->get_result();

        $lNum = $lResult->num_rows;
        if (0 == $lNum) {
            output_json_api_error($response, -8, 'email_no error');
            exit;
        }
        $row = $lResult->fetch_assoc();
        $email_no = $row['email_no'];
        $lEmail =  $row['email'];
        $lCode =  $row['code'];
        $lState =  intval($row['state']);
        $lChannel = intval($row['channel']);
        $lTryNum =  intval($row['try_num']);
        $lMins =  intval($row['mins']);
        if ($lChannel != $channel) {
            output_json_api_error($response, -13, 'channel error!');
            exit;
        }
        if ($lEmail != $lEncName) {
            output_json_api_error($response, -9, 'account and email do not match');
            exit;
        }
        if (0 != $lState || $lMins > $EMAILCODEVALIDMINS || $lTryNum >= $EMAILCODEERRORCNT) {
            output_json_api_error($response, -10, 'verification code expired');
            exit;
        }
        if ($lCode != $email_code) {
            $gConn->query("UPDATE t_hw_email_verify_no SET try_num=try_num+1 WHERE email_no = '" . $email_no . "'");
            output_json_api_error($response, -11, 'verification code error');
            exit;
        }
        $gConn->query("UPDATE t_hw_email_verify_no SET state=1 WHERE email_no = '" . $email_no . "'");

        $lResult->free();
    }
    $lStmt->close();
}

// if ($SEX_MALE != $sex && $SEX_FEMALE != $sex && $SEX_OTHER != $sex) {
//     $sex = $SEX_OTHER;
// }


$gResult = $gConn->query("SELECT id,icon_url FROM t_hw_user_avatar_list WHERE id={$icon_id}");
if (!$gResult) {
    output_json_db_error($response);
    exit;
} else {
    $lNum = $gResult->num_rows;

    if (1 == $lNum) {
        $row = $gResult->fetch_assoc();
        // $icon_url = $SERVER_RES_PREFIX[$SERVER_REGION_CUR_IDX] . $row['icon_url'];
        $icon_url = $SERVER_RES_PREFIX[$SERVER_REGION_CUR_IDX] .  "avatar/" . trim($row['icon_url']);
    } else {
        $icon_id = 0;
        $icon_url = '';
    }
    $gResult->free();
}

// $password = password_hash($passwd, PASSWORD_DEFAULT); //password_verify()
$lMyIP = get_ip();
$last_province = '未知';
$last_city = '未知';
$lRegIPCID = 0;

try {
    $reader = new Reader('./GeoIP2/GeoLite2-City.mmdb');
    $record = $reader->city($lMyIP);

    $lRegIPCode = trim($record->country->isoCode);
    $lRegIPCID = 0;
    $gResult = $gConn->query("SELECT id FROM t_hw_country_list WHERE two_letter='{$lRegIPCode}'");
    if ($gResult) {
        $lNum = $gResult->num_rows;

        if (1 == $lNum) {
            $row = $gResult->fetch_assoc();
            $lRegIPCID = intval($row['id']);
        }
    }

    if ($lRegIPCode == 'CN') {
        if (isset($record->city) && isset($record->city->names) && isset($record->city->names['zh-CN'])) {
            $last_city = str_replace('市', '', $record->city->names['zh-CN']);
        }
        if (isset($record->subdivisions) && isset($record->subdivisions[0]) && isset($record->subdivisions[0]->names) && isset($record->subdivisions[0]->names['zh-CN'])) {
            $last_province = $record->subdivisions[0]->names['zh-CN'];
        } else {
            $last_province = '中国';
        }
    } else {
        if (isset($record->country) && isset($record->country->names) && isset($record->country->names['zh-CN'])) {
            $last_province = $record->country->names['zh-CN'];
        }
    }
} catch (\Throwable $th) {
    Log::DEBUG("解析IP地址失败: " . $lMyIP . " , " . $th);
    if ($region_id == $SERVER_REGION_IDX_CHINA2 || $region_id == $SERVER_REGION_IDX_CHINA_MAINLAND) {
        $lRegIPCID = 211;
        $last_province = '中国';
    } else {
        $lRegIPCID = 0;
        $last_province = '未知';
    }
}


$gConn->begin_transaction();

$lBindRecordId = 0;

if ($r_dev_cpu != $dev_cpu || $r_dev_gpu != $dev_gpu || $r_dev_android != $dev_android || $r_dev_ram != $dev_ram || $r_dev_rom != $dev_rom || $r_dev_hdmi != $dev_hdmi || $r_dev_screen_w != $dev_screen_w || $r_dev_screen_h != $dev_screen_h) {
    if ($r_dev_regnum >= $DEVICE_REG_LIMIT) {
        output_json_api_error($response, -12, 'Maximum number of bindings');
        $gConn->rollback();
        exit;
    } else {
        $REG_CHANNEL_SQL = '';
        if ($r_dev_regnum == 0) {
            $REG_CHANNEL_SQL = ",reg_channel={$channel}";
        }
        try {
            $lStmt = $gConn->prepare("UPDATE t_hw_chip_list SET dev_regnum=dev_regnum+1, dev_cpu=?, dev_gpu=?, dev_android=?, dev_ram=?, dev_rom=?, dev_hdmi=?, dev_screen_w=?, dev_screen_h=?, lastregip=?, last_channel=? $REG_CHANNEL_SQL WHERE macid=?");
            $lStmt->bind_param(
                "sssssssssis",
                $dev_cpu,
                $dev_gpu,
                $dev_android,
                $dev_ram,
                $dev_rom,
                $dev_hdmi,
                $dev_screen_w,
                $dev_screen_h,
                $lMyIP,
                $channel,
                $real_macid
            );
            $lStmt->execute();
            $lStmt->close();
        } catch (\Throwable $th) {
            output_json_db_error($response);
            $gConn->rollback();
            Log::DEBUG("Error sql: " . $lSQL);
            Log::DEBUG("Update chip list exception: " . $th);
            Log::DEBUG("Update chip list error: " . mysqli_error($gConn));
            Log::DEBUG("Register info: " . json_encode($_POST));
            exit;
        }
        try {
            $lStmt = $gConn->prepare("INSERT INTO t_hw_chip_bind_record (macid, ip, channel, dev_cpu, dev_gpu, dev_android, dev_ram, dev_rom, dev_hdmi, dev_screen_w, dev_screen_h) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
            $lStmt->bind_param(
                "ssissssssss",
                $real_macid,
                $lMyIP,
                $channel,
                $dev_cpu,
                $dev_gpu,
                $dev_android,
                $dev_ram,
                $dev_rom,
                $dev_hdmi,
                $dev_screen_w,
                $dev_screen_h
            );
            $gResult = $lStmt->execute();
            $lStmt->close();
            if ($gResult) {
                $lCMD = "SELECT LAST_INSERT_ID() AS id";
                $lResult = $gConn->query($lCMD);
                if (!$lResult) {
                    output_json_db_error($response);
                    $gConn->rollback();
                    exit;
                } else {
                    $lRow = $lResult->fetch_assoc();
                    $lBindRecordId = intval($lRow['id']);

                    $lResult->free();
                }
            }
        } catch (\Throwable $th) {
            output_json_db_error($response);
            $gConn->rollback();
            exit;
        }
    }
}

$register_date =  date("Y-m-d H:i:s");
try {
    $lSQL = "INSERT INTO t_all_gameusers (name, nickname, icon_url, register_date, ip, reg_ip_country, reg_region, server_region_idx, reg_channel, macid, ptype, last_province, last_city, last_login_ip, app_type) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    $lStmt = $gConn->prepare($lSQL);
    $ptype = 1;
    $lStmt->bind_param(
        "sssssiiiisisssi",
        $lEncName,
        $nickname,
        $icon_url,
        $register_date,
        $lMyIP,
        $lRegIPCID,
        $region_id,
        $SERVER_REGION_CUR_IDX,
        $channel,
        $real_macid,
        $ptype,
        $last_province,
        $last_city,
        $lMyIP,
        $GLOBAL_MATCH_APP
    );
    $gResult = $lStmt->execute();
    $lStmt->close();
} catch (\Throwable $th) {
    output_json_db_error($response);
    $gConn->rollback();
    Log::DEBUG("Error sql: " . $lSQL);
    Log::DEBUG("Insert gameusers exception: " . $th);
    Log::DEBUG("Insert gameusers error: " . mysqli_error($gConn));
    Log::DEBUG("Register info: " . json_encode($_POST));
    exit;
}


$lCMD = "SELECT user_id,name,nickname,icon_url,reg_region FROM t_all_gameusers WHERE name= ?";
$lStmt = $gConn->prepare($lCMD); //mysql client --> mysql server(network time)???
if (!$lStmt) {
    output_json_db_error($response);
    $gConn->rollback();
    exit;
} else {
    $lStmt->bind_param("s", $lEncName);

    if (!$lStmt->execute()) { //mysql client --> mysql server(network time)???
        output_json_db_error($response);
        $gConn->rollback();
        exit;
    } else {
        $lResult = $lStmt->get_result();

        $lNum = $lResult->num_rows;

        if (0 == $lNum) {
            output_json_db_error($response);
            $gConn->rollback();
            exit;
        }

        //fill response
        $row = $lResult->fetch_assoc();

        $response->user_id = intval($row['user_id']);
        $response->name = $name;
        $response->nickname = $row['nickname'];

        $response->icon_url = trim($row['icon_url']);

        // $response->server_region_idx = intval($row['server_region_idx']);
        // $response->abode_country = intval($row['abode_country']);
        $response->region_id = intval($row['reg_region']);
        // $response->real_country = intval($row['real_country']);

        $gConn->query("UPDATE t_hw_chip_list SET user_id={$response->user_id},user_reg_time=now() WHERE macid='{$real_macid}' ");
        if ($lBindRecordId > 0) {
            $gConn->query("UPDATE t_hw_chip_bind_record SET user_id={$response->user_id} WHERE id={$lBindRecordId} ");
        }

        $lResult->free();
    }

    $lStmt->close();
}


// 建立背包
$gResult = $gConn->query("INSERT INTO t_hw_users_package (user_id) VALUES ({$response->user_id})");
if (!$gResult) {
    output_json_db_error($response);
    $gConn->rollback();
    exit;
}

$gConn->commit();



//-------------------------------------------------------------------------------------------
//make new beibao
// $gResult_package = $gConn->query("INSERT INTO T_snkvs_users_package (user_id,tx_using,tx_all,txk_using,txk_all,bq_all) VALUES ({$response->user_id},{$icon_id},'',{$txk_id},'{$txk_id}','') ");

//make extra info
$gConn->query("INSERT INTO t_all_gameusers_extra_info (user_id) VALUES ({$response->user_id}) ");
//-------------------------------------------------------------------------------------------

//new login token
$response->login_token = get_new_login_token($gConn, $response->user_id, $channel);


/*
//----------------------- START -----------------------------------------
if (strlen($response->login_token) > 0) {
    $gConn->close();

    //connect database and query
    $gConn = db_connect();

    if (!$gConn) {
        output_json_db_error($response);
        exit;
    }
    //Master/Slave ???
    $lRetryCnt = 6;
    while ($lRetryCnt--) {

        $lTokenTable = 't_hw_users_logintoken' . '_' . $SERVER_REGION_NAME_ARRAY[$SERVER_REGION_CUR_IDX];

        $lResult = $gConn->query("SELECT user_id,login_token FROM {$lTokenTable} WHERE user_id=" . $response->user_id . "  ");

        if ($lResult) {
            $result_num_token = $lResult->num_rows;

            if ($result_num_token > 0) {
                $row = $lResult->fetch_assoc();
                $r_login_token =  $row['login_token'];
                if (trim($response->login_token) == trim($r_login_token)) {
                    break;
                }
            } else {
                //continue;
            }

            $lResult->free();
        }

        sleep(1);
    }
}
//----------------------- END -----------------------------------------
 */

header('Content-type: application/json');
echo json_encode($response);

$gConn->close();
