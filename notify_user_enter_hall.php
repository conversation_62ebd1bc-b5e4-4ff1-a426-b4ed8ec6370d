<?php

include(dirname(__FILE__) . "/games_funs.php");

//---------------- JSON DATA CLASS ---------------------------

class GLResponse
{
    public $msg = 'ok';
    public $ret = 0;

};

$response = new GLResponse();
//-----------------------------------------------------------

$user_id = $_POST['user_id'];
$login_token = $_POST['login_token'];
$product_id = $_POST['device_id'];

$game_id = $_POST['game_id'];
$server_idx = $_POST['server_idx'];

$token = $_POST['token'];

if (NULL === $user_id 
    || NULL === $login_token
    || NULL === $product_id
    || NULL === $game_id
    || NULL === $server_idx
    || NULL === $token
) {
    output_json_para_error($response);
    exit;
}

$user_id = intval($user_id);
$product_id = trim($product_id);
$login_token = trim($login_token);
$game_id = intval($game_id);
$server_idx = intval($server_idx);
$token = trim($token);

if (sha1($user_id.$product_id.$game_id.$server_idx.$VS_KEY) != $token) {
    output_json_token_error($response);
    exit;
}
//-----------------------------------------------------------

$gConn = db_connect();

if (!$gConn) {
    output_json_db_error($response);
    exit;
}

$product_id = mysqli_real_escape_string($gConn, $product_id);
if (!is_chip_id_exist($gConn, $response, $product_id)) {
    exit;
}

if (!is_login_token_valid($gConn, $response, $user_id, $login_token)) {
    exit;
}

//-----------------------------------------------------------
header('Content-type: application/json');
echo json_encode($response);

$gConn->close();
