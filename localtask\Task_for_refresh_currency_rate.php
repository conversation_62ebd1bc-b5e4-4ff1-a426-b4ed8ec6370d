<?php

require 'vendor/autoload.php';

include(dirname(__FILE__) . "/../games_funs.php");
include(dirname(__FILE__) . "/../log.php");


//--------------------------------------------------------------------------------

if (!isset($argc) || !isset($argv) || 2 != $argc || '2OWSXYsZCMMU0GLd' != $argv[1]) {
    echo "QUIT";
    exit;
}

//--------------------------------------------------------------------------------

$gConn = db_connect();

if (!$gConn) {
    exit;
}

//初始化日志
$logHandler = new CLogFileHandler(sys_get_temp_dir() . DIRECTORY_SEPARATOR . "openexchangerate_refresh.log");
$log = Log::Init($logHandler, 1);

// get paypal token
$paypalAccessToken = "";
$openexchangerateReturnBody = WpOrg\Requests\Requests::get('https://openexchangerates.org/api/latest.json?app_id=' . $OPENEXCHANGERATES_APPID, array(), array("timeout" => 30, "connect_timeout" => 30));
if ($openexchangerateReturnBody->status_code == 200) {
    $openexchangerateData = json_decode($openexchangerateReturnBody->body);
    print_r($openexchangerateData);
    if ($openexchangerateData->rates) {
        $rates = $openexchangerateData->rates;
        $base = $openexchangerateData->base;
        foreach ($rates as $key => $value) {
            $stmt = $gConn->prepare("REPLACE INTO t_exchange_rates (currency_code, base, rate, last_updated) VALUES (?, ?, ?, NOW())");
            $stmt->execute([$key, $base, $value]);
        }
    }
} else {
    Log::Debug("error,status_code: $openexchangerateReturnBody->status_code, $openexchangerateReturnBody->body");
    exit;
}
