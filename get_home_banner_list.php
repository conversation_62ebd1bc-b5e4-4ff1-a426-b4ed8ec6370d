<?php

include(dirname(__FILE__) . "/games_funs.php");

//---------------- JSON DATA CLASS ---------------------------

class GLResponse
{
    public $msg = 'ok';
    public $ret = 0;

    public $banner_list = NULL;
};

class BannerItem
{

    public $id = 0;
    public $icon_url = '';
    public $goto_type = ''; // 0: 无跳转 1:跳转网页 2:打开视频
    public $goto_url = ''; // 网页/视频url
    public $game_id = -1;  // 映射到的游戏ID
}

$response = new GLResponse();
//-----------------------------------------------------------

$user_id = $_GET['user_id'];
$login_token = $_GET['login_token'];
$product_id = $_GET['device_id'];
$lang = $_GET['lang'];
$channel = $_GET['channel'];
$token = $_GET['token'];

if ($user_id == NULL || $login_token == NULL || $product_id == NULL ||  $lang == NULL || $channel == NULL || $token == NULL) {
    output_json_para_error($response);
    exit;
}

$user_id = intval($user_id);
$lang = intval($lang);
$channel = intval($channel);
$product_id = trim($product_id);
$login_token = trim($login_token);
$token = trim($token);

if (sha1($user_id . $product_id . $lang . $channel . $VS_KEY) != $token) {
    output_json_token_error($response);
    exit;
}
//-----------------------------------------------------------

$gConn = db_connect();

if (!$gConn) {
    output_json_db_error($response);
    exit;
}

$product_id = mysqli_real_escape_string($gConn, $product_id);

// if (!is_chip_id_exist($gConn, $response, $product_id)) {
//     exit;
// }

if (!is_login_token_valid($gConn, $response, $user_id, $login_token)) {
    exit;
}

$result = $gConn->query("SELECT id,icon_url,goto_type,goto_url,game_id FROM t_hw_home_banner WHERE (region_id=-1 OR region_id={$SERVER_REGION_CUR_IDX}) AND start_time<=now() AND end_time>now() AND is_show=1 ORDER BY position,id DESC");
if (!$result) {
    output_json_db_error($response);
    exit;
} else {

    $result_num = $result->num_rows;
    for ($i = 0; $i < $result_num; $i++) {
        $row = $result->fetch_assoc();

        $lItem = new BannerItem();
        $lItem->id =  intval($row['id']);
        if (strlen(trim($row['icon_url'])) > 0) {
            $lItem->icon_url = $SERVER_RES_PREFIX[$SERVER_REGION_CUR_IDX] . trim($row['icon_url']);
        }
        $lItem->game_id = intval($row['game_id']);
        $lItem->goto_type = intval($row['goto_type']);
        $lItem->goto_url = trim($row['goto_url']);

        $response->banner_list[$i] = $lItem;
    }
    $result->free();
}


//-----------------------------------------------------------
header('Content-type: application/json');
echo json_encode($response);

$gConn->close();
