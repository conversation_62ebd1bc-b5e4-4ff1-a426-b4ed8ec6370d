<?php

include(dirname(__FILE__) . "/games_funs.php");
include(dirname(__FILE__) . "/PasswordHash.php");

function seed()
{
    list($msec, $sec) = explode(' ', microtime());
    return (float) $sec;
}

srand(seed());

//---------------- JSON DATA CLASS ---------------------------

class GLResponse
{

    public $msg = 'ok';
    public $ret = 0;
    public $error_code = ERROR_NOERROR;
};

$response = new GLResponse();

$user_id = $_POST['user_id'];
$email_no = $_POST['email_no'];
$email_code = $_POST['email_code'];
$product_id = $_POST['device_id'];
$channel = $_POST['channel'];
$token = $_POST['token'];
$login_token = $_POST['login_token'];

if (NULL == $user_id || NULL == $email_no || NULL == $email_code || NULL == $product_id || $channel == NULL || NULL == $token || NULL == $login_token) {
    output_json_para_error_msg($response, 'args is NULL!');
    exit;
}

$user_id = intval($user_id);
$product_id = trim($product_id);
$email_no = trim($email_no);
$email_code = trim($email_code);
$channel = intval($channel);
$login_token = trim($login_token);
$token = trim($token);

if (sha1($user_id . $email_no . $email_code . $product_id . $channel . $VS_KEY) != $token) {
    output_json_token_error($response);
    exit;
}

//-----------------------------------------------------------

$lAES = new AESSSL($EMAILAES128KEY);

//connect database and query
$gConn = db_connect();

if (!$gConn) {
    output_json_db_error($response);
    exit;
}

$email_no = mysqli_real_escape_string($gConn, $email_no);
$product_id = mysqli_real_escape_string($gConn, $product_id);

if (!is_chip_id_exist($gConn, $response, $product_id)) {
    exit;
}

if (!is_login_token_valid($gConn, $response, $user_id, $login_token)) {
    exit;
}

$name = '';
$lEncName = '';
$nickname = '';

$result = $gConn->query("SELECT name,nickname FROM t_all_gameusers WHERE user_id={$user_id}");

if (!$result) {
    output_json_db_error($response);
    exit;
} else {
    $result_num = $result->num_rows;

    if ($result_num > 0) {
        $row = $result->fetch_assoc();
        $lEncName = $row['name'];
        $name = $lAES->decrypt($lEncName);
        $nickname = $row['nickname'];
    }

    $result->free();
}

$gResult = $gConn->query("SELECT channel FROM t_hw_all_channel WHERE channel={$channel}");
if (!$gResult) {
    output_json_db_error($response);
    exit;
} else {
    $lNum = $gResult->num_rows;

    if ($lNum != 1) {
        output_json_api_error($response, -4, 'channel error!');
        exit;
    }

    $gResult->free();
}

$result = $gConn->query("SELECT email,code,state,try_num,channel,TIMESTAMPDIFF(MINUTE, `time`, now() ) AS mins FROM t_hw_email_verify_no_rm_account WHERE email_no = '{$email_no}' AND user_id={$user_id}");

if (!$result) {
    output_json_db_error($response);
    exit;
} else {
    $result_num = $result->num_rows;
    if (0 == $result_num) {
        output_json_register_error2($response, -5, 'email_no error');
        exit;
    }
    $row = $result->fetch_assoc();
    $lEmail =  $row['email'];
    $lCode =  $row['code'];
    $lState =  intval($row['state']);
    $lTryNum =  intval($row['try_num']);
    $lMins =  intval($row['mins']);
    $lChannel = intval($row['channel']);
    if ($lChannel != $channel) {
        output_json_api_error($response, -4, 'channel error!');
        exit;
    }
    if ($lEmail != $lEncName) {
        output_json_register_error2($response, -6, 'account and email do not match');
        exit;
    }
    if (0 != $lState || $lMins > 15 || $lTryNum >= $EMAILCODEERRORCNT) {
        output_json_register_error2($response, -7, 'verification code expired');
        exit;
    }
    if ($lCode != $email_code) {
        $gConn->query("UPDATE t_hw_email_verify_no_rm_account SET try_num=try_num+1 WHERE email_no = '" . $email_no . "'");
        output_json_register_error2($response, -8, 'verification code error');
        exit;
    }
    $gConn->query("UPDATE t_hw_email_verify_no_rm_account SET state=1 WHERE email_no = '" . $email_no . "'");

    $result->free();
}

//remove login token ???
$lTokenTable = 't_hw_users_logintoken';
$gConn->query("UPDATE $lTokenTable SET login_token='empty{$login_token}' WHERE user_id={$user_id}");

//remove name/nickname ???
$gConn->query("INSERT INTO t_hw_gameusers_rm_account (user_id, name, nickname)  VALUES ({$user_id},'{$lEncName}','{$nickname}')");
$gConn->query("UPDATE t_all_gameusers SET name='Empty{$user_id}',nickname='' WHERE user_id={$user_id}");

//remove game win/lose record ???
for ($idx = 0; $idx < 150; $idx++) {

    switch ($idx) {
        case 0:
        case 1:
        case 2:
        case 4:
        case 5:
        case 6:
        case 7:
        case 8:
        case 9:
        case 14:
        case 15:
        case 16:
        case 17:
        case 18:
        case 19:
        case 20:
        case 21:
        case 22:
        case 23:
        case 24:
        case 25:
        case 26:
        case 27:
        case 28:
        case 29:
        case 30:
            $result = $gConn->query("SELECT * FROM t_hw_user_scores_game_{$idx} WHERE user_id={$user_id}");
            if ($result) {
                $result_num = $result->num_rows;
                if (1 == $result_num) {
                    $row = $result->fetch_assoc();

                    $lWin = intval($row['win']);
                    $lLose = intval($row['lose']);
                    $lDraw = intval($row['draw']);
                    $lEscape = intval($row['escape']);
                    $lPoints = intval($row['points']);
                    $lLevel = intval($row['level']);

                    $gConn->query("INSERT INTO t_hw_gameusers_vs_rm_account (user_id, game_id, win, lose, draw, points, level, escape)  VALUES ({$user_id},{$idx},{$lWin},{$lLose},{$lDraw},{$lPoints},{$lLevel},{$lEscape})");
                    $gConn->query("DELETE FROM t_hw_user_scores_game_{$idx} WHERE user_id={$user_id}");
                }
                $result->free();
            }
            break;

        default:
            break;
    }
}


header('Content-type: application/json');
echo json_encode($response);

$gConn->close();
