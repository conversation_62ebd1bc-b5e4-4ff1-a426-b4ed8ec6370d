<?php

include(dirname(__FILE__) . "/games_funs.php");





$gameIds = '300,301,302,303,304';

$gameIdArr = explode(',', $gameIds);

$gConn = db_connect();

if (!$gConn) {
    echo "connect db error";
    exit;
}

$gConn->begin_transaction();

try {
    $gResult = $gConn->query("SELECT channel,include_game FROM t_hw_all_channel");
} catch (\Throwable $th) {
    echo $th;
    exit;
}

if (!$gResult) {
    echo mysqli_error($gConn);
    exit;
} else {
    $lNum = $gResult->num_rows;

    for ($i = 0; $i < $lNum; $i++) {
        $lRow = $gResult->fetch_assoc();

        $lChannel = intval($lRow['channel']);
        $lIncludeGame = trim($lRow['include_game']);

        if ($lIncludeGame == '-1') {
            continue;
        }

        $lIncludeGameArr = explode(',', $lIncludeGame);
        foreach ($gameIdArr as $key => $value) {
            if (!in_array($value, $lIncludeGameArr)) {
                $lIncludeGameArr[] = $value;
            }
        }
        $lNewIncludeGame = join(',', $lIncludeGameArr);
        try {
            $gConn->query("UPDATE t_hw_all_channel SET include_game='{$lNewIncludeGame}' WHERE channel={$lChannel}");
        } catch (\Throwable $th) {
            $gConn->rollback();
            echo $th;
            exit;
        }
    }

    $gResult->free();
}


try {
    $result = $gConn->query("SELECT region_id,include_game FROM t_hw_all_server_region");
} catch (\Throwable $th) {
    $gConn->rollback();
    echo $th;
    exit;
}
if (!$result) {
    $gConn->rollback();
    echo mysqli_error($gConn);
    exit;
} else {
    $result_num = $result->num_rows;

    for ($i = 0; $i < $result_num; $i++) {
        $row = $result->fetch_assoc();

        $lRegionId = intval($row['region_id']);
        $lIncludeGame = trim($row['include_game']);

        if ($lIncludeGame == '-1') {
            continue;
        }

        $lIncludeGameArr = explode(',', $lIncludeGame);
        foreach ($gameIdArr as $key => $value) {
            if (!in_array($value, $lIncludeGameArr)) {
                $lIncludeGameArr[] = $value;
            }
        }
        $lNewIncludeGame = join(',', $lIncludeGameArr);
        try {
            $gConn->query("UPDATE t_hw_all_server_region SET include_game='{$lNewIncludeGame}' WHERE region_id={$lRegionId}");
        } catch (\Throwable $th) {
            $gConn->rollback();
            echo $th;
            exit;
        }
    }

    $result->free();
}


$gConn->commit();

$gConn->close();

echo join(',', $gameIdArr);
