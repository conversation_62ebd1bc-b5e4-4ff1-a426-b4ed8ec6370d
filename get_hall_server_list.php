<?php

include(dirname(__FILE__) . "/games_funs.php");

//---------------- JSON DATA CLASS ---------------------------

class GLResponse
{
    public $msg = 'ok';
    public $ret = 0;

    public $server_list = NULL;
};

class SERVERINFO{

	public $server_idx = -1;
	public $server_name = '';
	public $room_ip = '';
	public $room_port = 0;
	public $watch_ip = '';
	public $watch_port = 0;
};

$response = new GLResponse();
//-----------------------------------------------------------

$user_id = $_GET['user_id'];
$login_token = $_GET['login_token'];
$product_id = $_GET['device_id'];
$token = $_GET['token'];

if ($user_id == NULL || $login_token == NULL || $product_id == NULL || $token == NULL) {
    output_json_para_error($response);
    exit;
}

$user_id = intval($user_id);
$product_id = trim($product_id);
$login_token = trim($login_token);
$token = trim($token);

if (sha1($user_id . $product_id . $VS_KEY) != $token) {
    output_json_token_error($response);
    exit;
}
//-----------------------------------------------------------

$gConn = db_connect();

if (!$gConn) {
    output_json_db_error($response);
    exit;
}

$product_id = mysqli_real_escape_string($gConn, $product_id);

if (!is_chip_id_exist($gConn, $response, $product_id)) {
    exit;
}

if (!is_login_token_valid($gConn, $response, $user_id, $login_token)) {
    exit;
}

$result = $gConn->query("SELECT * FROM t_hw_all_hall_servers WHERE isshow=1 ORDER BY server_idx ASC");

if(!$result)
{
    output_json_db_error($response);
    exit;
}else{

    $result_num = $result->num_rows;

    for($i=0;$i<$result_num;$i++)
    {
        $row = $result->fetch_assoc();

        $lItem = new SERVERINFO();

        $lItem->server_idx = $row['server_idx'];
        $lItem->server_name = $row['name'];
        $lItem->room_ip = $row['room_ip'];
        $lItem->room_port = intval($row['room_port']);
        $lItem->watch_ip = $row['watch_ip'];
        $lItem->watch_port = intval($row['watch_port']);
        
        $response->server_list[$i] = $lItem;
    }
    
    $result->free();
}

//-----------------------------------------------------------
header('Content-type: application/json');
echo json_encode($response);

$gConn->close();
