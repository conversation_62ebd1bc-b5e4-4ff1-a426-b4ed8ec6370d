<?php

include(dirname(__FILE__) . "/games_funs.php");


class GLResponse
{
    public $msg = 'ok';
    public $ret = 0;

    public $state = 0; // 订单状态 0:进行中 1:已完成 -1:金额不匹配 -2:货币类型不匹配
};

$response = new GLResponse();
//-----------------------------------------------------------

$user_id = $_GET['user_id'];
$login_token = $_GET['login_token'];
$product_id = $_GET['device_id'];
$lang = $_GET['lang'];
$channel = $_GET['channel'];
$trade_no = $_GET['trade_no'];
$token = $_GET['token'];

if ($user_id == NULL || $login_token == NULL || $product_id == NULL || $lang === NULL || $channel == NULL || $trade_no == NULL || $token == NULL) {
    output_json_para_error($response);
    exit;
}

$user_id = intval($user_id);
$lang = intval($lang);
$channel = intval($channel);
$product_id = trim($product_id);
$login_token = trim($login_token);
$trade_no = trim($trade_no);
$token = trim($token);

if (sha1($user_id . $product_id . $lang . $channel . $trade_no . $VS_KEY) != $token) {
    output_json_token_error($response);
    exit;
}

$language_id = $gDLangTSuffix;
if (isset($gAllLanguage[$lang])) {
    $language_id = $lang;
}

//-----------------------------------------------------------

$gConn = db_connect();

if (!$gConn) {
    output_json_db_error($response);
    exit;
}

$product_id = mysqli_real_escape_string($gConn, $product_id);
$trade_no = mysqli_real_escape_string($gConn, $trade_no);

if (!is_login_token_valid($gConn, $response, $user_id, $login_token)) {
    exit;
}


$lCMD = "SELECT state,business_state FROM t_hw_recharge_trade_no WHERE user_id={$user_id} AND trade_no = ?";
$lStmt = $gConn->prepare($lCMD);
if (!$lStmt) {
    output_json_db_error_with_msg($response, mysqli_error($gConn));
    exit;
} else {
    $lStmt->bind_param("s", $trade_no);

    if (!$lStmt->execute()) { //mysql client --> mysql server(network time)???
        output_json_db_error_with_msg($response, mysqli_error($aConn));
        exit;
    } else {
        $lResult = $lStmt->get_result();

        $lNum = $lResult->num_rows;

        if ($lNum < 1) {
            output_json_api_error($response, -4, 'trade not exist!');
            exit;
        }

        $row = $lResult->fetch_assoc();

        $state = intval($row['state']);
        $business_state = intval($row['business_state']);

        if ($business_state == 100 && $state > 0) {
            $response->state = 1;
        } else if ($state < 0) {
            $response->state = $state;
        }

        $lResult->free();
    }

    $lStmt->close();
}


header('Content-type: application/json');
echo json_encode($response);

$gConn->close();
