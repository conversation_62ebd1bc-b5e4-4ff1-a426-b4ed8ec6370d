<?php

include (dirname(__FILE__)."/games_funs.php");

include (dirname(__FILE__)."/player_common_struct.php");

//---------------- JSON DATA CLASS ---------------------------

class GLResponse{

    public $msg = 'ok';
    public $ret = 0;

    public $playerGameInfoList = NULL;    
};

class PlayerGameInfo{
    public $playerInfo = NULL;
    public $vsGameInfo = NULL;
    public $coopGameInfo = NULL;
}

class VSGameInfo{

    public $level = 1;
    public $rankLevel = 1;
    public $levelExp = 0;
    public $levelTotalExp = 500;

    public $win = 0;
    public $lose = 0;
    public $draw = 0;
    public $escape = 0;
}

class CooperativeGameInfo{

    public $level = 1;
    public $rankLevel = 1;
    public $levelExp = 0;
    public $levelTotalExp = 500;

    public $single_tg_num = 0;
    public $multi_tg_num = 0;
}

//-----------------------------------------------------------

$response = new GLResponse();

//args
$user_id = $_GET['user_id'];
$login_token = $_GET['login_token'];
$product_id = $_GET['device_id'];

$game_id = $_GET['game_id'];
$players_id = $_GET['players_id'];//11-22-33

$token = $_GET['token'];

/*
$user_id = 8;
$login_token = 'e33ae3984260e1b1a8dfbf8af86f509c0a3b162e';
$product_id = 'aaa';

$game_id = 0;
$players_id = "1000-1001";

$token = 'aaaaa';
 */

if(NULL === $user_id
    || NULL === $login_token 
    || NULL === $product_id
    || NULL === $game_id
    || NULL === $players_id
    || NULL === $token
){
    output_json_para_error($response);
    exit;
}else{

    $user_id = intval($user_id);
    $login_token = trim($login_token);
    $product_id = trim($product_id);
    
    $game_id = intval($game_id);
    $players_id = trim($players_id);

    $token = trim($token);

    if($user_id <= 0
        || $LOGINTOKENLEN != strlen($login_token)
        || 0 == strlen($token)
        || 0 == strlen($players_id)
        || !isValidIDStr($players_id)
    ){
        output_json_para_error($response);
        exit;
    }
}

if (sha1($user_id.$product_id.$game_id.substr($players_id,0,20).$VS_KEY) != $token)
{
    output_json_token_error($response);
    exit;
}

$gConn = db_connect();

if(!$gConn)
{
    output_json_db_error($response);
    exit;
}

$product_id = mysqli_real_escape_string($gConn, $product_id);
if (!is_chip_id_exist($gConn, $response, $product_id)) {
    exit;
}

if(!is_login_token_valid($gConn,$response,$user_id,$login_token)){ exit; }

$gContentType = 0;
$lResult = $gConn->query("SELECT content_type FROM t_hw_all_games_common WHERE game_id={$game_id}");
if (!$lResult) 
{
    output_json_db_error_with_msg($response,mysqli_error($gConn));
    exit;
}else{
    $lNum = $lResult->num_rows;
    
    if(1 != $lNum){
        output_json_db_error_with_msg($response,"Game id error");
        exit;
    }else{
        $lRow = $lResult->fetch_assoc();
        $gContentType = intval($lRow['content_type']);
    }
}
//--------------------------------------------------------------------------------

$user_id_array;
$gIDX = 0; 

$MAXUSERPR = 30;
if($players_id != NULL)
{
    $user_id_array = explode('-', $players_id);
    $user_id_array = array_unique($user_id_array);
    $lUserCnt = sizeof($user_id_array);

    $lUserRow = intval($lUserCnt/$MAXUSERPR);
    $lUserCol = intval($lUserCnt%$MAXUSERPR);
    $lUserExtra = 0;
    if($lUserCol > 0){
        $lUserExtra = 1;
    }
    for($i=0;$i<($lUserRow+$lUserExtra);$i++){
        $lCurUserIDArray = [];
        $g_user_id_str = '';
        $k = 0;
        $lMAXUSERPR = ($i==$lUserRow) ? $lUserCol : $MAXUSERPR;
        for($idx=0;$idx<$lMAXUSERPR;$idx++){
            $lCurUserID = intval($user_id_array[$i*$MAXUSERPR+$idx]);
            if($lCurUserID > 0){
                $user[$lCurUserID] = new PlayerGameInfo();
                
                if(0 == $gContentType){
                    $user[$lCurUserID]->vsGameInfo = new VSGameInfo();
                }else{
                    $user[$lCurUserID]->coopGameInfo = new CooperativeGameInfo();
                }

                $lCurUserIDArray[] = $lCurUserID;

                if($k == 0){
                    $g_user_id_str = "{$lCurUserID}";
                }else{
                    $g_user_id_str = "{$g_user_id_str},{$lCurUserID}";
                }
                $k++;
            }
        }
        if(0 == COUNT($lCurUserIDArray)) continue;

        $lPlayerInfoArray = getPlayerInfo($gConn,$lCurUserIDArray,TYPE_PINFO_SUB,true,EXTRA_PINFO_NULL);
        if(NULL == $lPlayerInfoArray){
            continue;
        }
        //Extra process for liveInfo
        foreach($lPlayerInfoArray as $key => $value){
            $lCurUserID = $key; 
            $user[$lCurUserID]->playerInfo = $lPlayerInfoArray[$lCurUserID]; 
        }

        if(0 == $gContentType){
            $lResult = $gConn->query("SELECT user_id,win,lose,draw,escape,level,points FROM t_hw_user_scores_game_{$game_id} WHERE user_id IN ({$g_user_id_str})");

            if (!$lResult) 
            {
                output_json_db_error_with_msg($response,mysqli_error($gConn));
                exit;
            }
            else
            {
                $lNum = $lResult->num_rows;

                for($lIDX=0;$lIDX<$lNum;$lIDX++){
                    //fill response
                    $lRow = $lResult->fetch_assoc();

                    $lCurUserID = intval($lRow['user_id']);

                    if(!isset($user[$lCurUserID]))
                        continue;

                    $user[$lCurUserID]->vsGameInfo->win = intval($lRow['win']);
                    $user[$lCurUserID]->vsGameInfo->lose = intval($lRow['lose']);
                    $user[$lCurUserID]->vsGameInfo->draw = intval($lRow['draw']);
                    $user[$lCurUserID]->vsGameInfo->escape = intval($lRow['escape']);

                    $lLevel = intval($lRow['level']);
                    $lPoints = intval($lRow['points']);

                    //------------------------------------------------------------
                    $user[$lCurUserID]->vsGameInfo->level = $lLevel;
                    $user[$lCurUserID]->vsGameInfo->rankLevel = getRankLevelFromLevel($lLevel);
                    getGDLevelExpFromPL($lPoints,$lLevel,$user[$lCurUserID]->vsGameInfo->levelTotalExp,$user[$lCurUserID]->vsGameInfo->levelExp);
                }

                $lResult->free();
            }
        }else{
            //TODO GG
        }

        //-----------------------------------------------------------------------------------
        foreach($user as $value){
            $response->playerGameInfoList[$gIDX] = $value;
            $gIDX++;
        }

        unset($lCurUserIDArray);
        unset($user);
    }
    //---------------------------------------------------------
}
 
header('Content-type: application/json');
echo json_encode($response);

$gConn->close();

?>
