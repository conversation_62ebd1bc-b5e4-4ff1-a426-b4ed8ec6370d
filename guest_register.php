<?php

include(dirname(__FILE__) . "/games_funs.php");
include(dirname(__FILE__) . "/PasswordHash.php");

require_once(dirname(__FILE__) . "/GeoIP2/geoip2.phar");
require_once './log.php';

use GeoIp2\Database\Reader;

//初始化日志 windwos下路径为：C:\Users\<USER>\AppData\Local\Temp
$logHandler = new CLogFileHandler(sys_get_temp_dir() . DIRECTORY_SEPARATOR . "reg_yz_handgame_" . date('Y-m-d') . '.log');
$log = Log::Init($logHandler, 1);


function seed()
{
    list($msec, $sec) = explode(' ', microtime());
    return (float) $sec;
}

srand(seed());

//---------------- JSON DATA CLASS ---------------------------

class GLResponse
{

    public $msg = 'ok';
    public $ret = 0;

    public $user_id = 0; //用户ID

    public $nickname = ''; //昵称
    public $icon_url = ''; //头像URL
    public $gold = 0; //剩余金币
    public $server_region_idx = -1; //大区服务器ID(注册时选定，后期不能更改)
    public $region_id = -1; //区域ID
    public $cur_server_region_idx = -1; //当前大区服务器ID,无异常时server_region_idx与其相同，有异常则不同
    public $login_token = ''; //登录token

    public $is_new = 1;
    public $ptype = 1; //0->游客,1->正常用户,2->知名玩家,3->机器人
};

$response = new GLResponse();

$product_id = $_POST['device_id'];
$region_id = $_POST['region_id'];
$channel = $_POST['channel'];
$token = $_POST['token'];

$dev_cpu = $_POST['dev_cpu'];
$dev_gpu = $_POST['dev_gpu'];
$dev_android = $_POST['dev_android'];
$dev_ram = $_POST['dev_ram'];
$dev_rom = $_POST['dev_rom'];
$dev_hdmi = $_POST['dev_hdmi'];
$dev_screen_w = $_POST['dev_screen_w'];
$dev_screen_h = $_POST['dev_screen_h'];

if (NULL == $token || NULL == $product_id || NULL == $region_id || NULL == $channel || NULL == $dev_cpu || NULL == $dev_gpu || NULL == $dev_android || NULL == $dev_ram || NULL == $dev_rom || NULL == $dev_hdmi || NULL == $dev_screen_w || NULL == $dev_screen_h) {
    output_json_para_error($response);
    exit;
}

$product_id = trim($product_id);
$region_id = intval($region_id);
$channel = intval($channel);

$dev_cpu = trim($dev_cpu);
$dev_gpu = trim($dev_gpu);
$dev_android = trim($dev_android);
$dev_ram = trim($dev_ram);
$dev_rom = trim($dev_rom);
$dev_hdmi = trim($dev_hdmi);
$dev_screen_w = trim($dev_screen_w);
$dev_screen_h = trim($dev_screen_h);

if (sha1($product_id . $region_id . $channel . $VS_KEY) != $token) {
    output_json_token_error($response);
    exit;
}

// 移除所有控制字符、不可见字符和 HTML 编码
$dev_ram = preg_replace('/[\x{200B}-\x{200F}\x{FEFF}\x{202A}-\x{202E}]+/u', '', $dev_ram);
$dev_rom = preg_replace('/[\x{200B}-\x{200F}\x{FEFF}\x{202A}-\x{202E}]+/u', '', $dev_rom);

// 移除可能的 HTML 编码形式（如 <200e>）
$dev_ram = preg_replace('/<200[eEfF]>/', '', $dev_ram);
$dev_rom = preg_replace('/<200[eEfF]>/', '', $dev_rom);

// if (strlen($dev_ram) > 64) {
//     $dev_ram = str_replace(["\u200e", "\u200f", "<200e>", "<200f>"], '', $dev_ram);
// }
// if (strlen($dev_rom) > 64) {
//     $dev_rom = str_replace(["\u200e", "\u200f", "<200e>", "<200f>"], '', $dev_rom);
// }

//connect database and query
$gConn = db_connect();

if (!$gConn) {
    output_json_db_error($response);
    exit;
}

$product_id = mysqli_real_escape_string($gConn, $product_id);

$r_dev_regnum = 0;
$r_dev_cpu = '';
$r_dev_gpu = '';
$r_dev_android = '';
$r_dev_ram = '';
$r_dev_rom = '';
$r_dev_hdmi = '';
$r_dev_screen_w = '';
$r_dev_screen_h = '';

$real_macid = '';

$gResult = $gConn->query("SELECT macid,dev_regnum,dev_cpu,dev_gpu,dev_android,dev_ram,dev_rom,dev_hdmi,dev_screen_w,dev_screen_h FROM t_hw_chip_list WHERE macid_enc='{$product_id}'");

if (!$gResult) {
    output_json_db_error($response);
    exit;
} else {

    $result_num = $gResult->num_rows;

    if ($result_num < 1) {
        Log::DEBUG('CHIP ID ERROR:' . $product_id);
        output_json_api_error($response, -2, 'chip id error!');
        exit;
    } else {
        $row = $gResult->fetch_assoc();

        $real_macid = trim($row['macid']);
        $r_dev_regnum = intval($row['dev_regnum']);
        $r_dev_cpu = $row['dev_cpu'];
        $r_dev_gpu = $row['dev_gpu'];
        $r_dev_android = $row['dev_android'];
        $r_dev_ram = $row['dev_ram'];
        $r_dev_rom = $row['dev_rom'];
        $r_dev_hdmi = $row['dev_hdmi'];
        $r_dev_screen_w = $row['dev_screen_w'];
        $r_dev_screen_h = $row['dev_screen_h'];
    }
    $gResult->free();
}

$lMyIP = get_ip();
$last_province = '未知';
$last_city = '未知';

$MAXIPREGCNT = 10;
$today = date("Y-m-d");
$result_limit = $gConn->query("SELECT COUNT(*) AS cnt FROM t_all_gameusers WHERE ip='{$lMyIP}' AND macid='{$real_macid}' AND register_date LIKE '{$today}%'");

if (!$result_limit) {
    output_json_db_error($response);
    exit;
} else {
    $result_limit_num = $result_limit->num_rows;

    if ($result_limit_num > 0) {
        $row_limit = $result_limit->fetch_assoc();
        $lCnt = intval($row_limit['cnt']);
        if ($lCnt >= $MAXIPREGCNT) {
            output_json_api_error($response, -3, 'guest account imit value reached in the device.');
            exit;
        }
    }

    $result_limit->free();
}

$gResult = $gConn->query("SELECT region_id FROM t_hw_all_server_region WHERE region_id={$region_id}");
if (!$gResult) {
    output_json_db_error($response);
    exit;
} else {
    $lNum = $gResult->num_rows;

    if ($lNum != 1) {
        output_json_api_error($response, -5, 'region error!');
        exit;
    }

    $gResult->free();
}

$gResult = $gConn->query("SELECT channel FROM t_hw_all_channel WHERE channel={$channel}");
if (!$gResult) {
    output_json_db_error($response);
    exit;
} else {
    $lNum = $gResult->num_rows;

    if ($lNum != 1) {
        output_json_api_error($response, -6, 'channel error!');
        exit;
    }

    $gResult->free();
}

$lRegIPCID = 0;

try {
    $reader = new Reader('./GeoIP2/GeoLite2-City.mmdb');
    $record = $reader->city($lMyIP);

    $lRegIPCode = trim($record->country->isoCode);

    if ($lRegIPCode == 'CN') {
        if (isset($record->city) && isset($record->city->names) && isset($record->city->names['zh-CN'])) {
            $last_city = str_replace('市', '', $record->city->names['zh-CN']);
        }
        if (isset($record->subdivisions) && isset($record->subdivisions[0]) && isset($record->subdivisions[0]->names) && isset($record->subdivisions[0]->names['zh-CN'])) {
            $last_province = $record->subdivisions[0]->names['zh-CN'];
        } else {
            $last_province = '中国';
        }
    } else {
        if (isset($record->country) && isset($record->country->names) && isset($record->country->names['zh-CN'])) {
            $last_province = $record->country->names['zh-CN'];
        }
    }

    $gResult = $gConn->query("SELECT id FROM t_hw_country_list WHERE two_letter='{$lRegIPCode}'");
    if ($gResult) {
        $lNum = $gResult->num_rows;

        if (1 == $lNum) {
            $row = $gResult->fetch_assoc();
            $lRegIPCID = intval($row['id']);
        }
    }
} catch (\Throwable $th) {
    Log::DEBUG("解析IP地址失败: " . $lMyIP . " , " . $th);
    if ($region_id == $SERVER_REGION_IDX_CHINA2 || $region_id == $SERVER_REGION_IDX_CHINA_MAINLAND) {
        $lRegIPCID = 211;
        $last_province = '中国';
    } else {
        $lRegIPCID = 0;
        $last_province = '未知';
    }
}


$lAES = new AESSSL($EMAILAES128KEY);

$name = $lAES->encrypt('Guest' . random_int(100000, 999999));
$nickname = 'Player_';
$password = '';
$icon_url = '';

$gResult = $gConn->query("SELECT icon_url FROM t_hw_user_avatar_list WHERE is_show=1 ");
if (!$gResult) {
    output_json_db_error($response);
    exit;
} else {
    $result_num = $gResult->num_rows;

    $rand_icon = mt_rand(0, $result_num - 1);

    for ($i = 0; $i < $result_num; $i++) {
        $row = $gResult->fetch_assoc();
        if ($i == $rand_icon) {
            $icon_url = $SERVER_RES_PREFIX[$SERVER_REGION_CUR_IDX] .  "avatar/" . trim($row['icon_url']);
            break;
        }
    }
}

$gConn->begin_transaction();

if ($r_dev_cpu != $dev_cpu || $r_dev_gpu != $dev_gpu || $r_dev_android != $dev_android || $r_dev_ram != $dev_ram || $r_dev_rom != $dev_rom || $r_dev_hdmi != $dev_hdmi || $r_dev_screen_w != $dev_screen_w || $r_dev_screen_h != $dev_screen_h) {
    if ($r_dev_regnum >= $DEVICE_REG_LIMIT) {
        output_json_api_error($response, -4, 'Maximum number of bindings');
        $gConn->rollback();
        exit;
    } else {
        $REG_CHANNEL_SQL = '';
        if ($r_dev_regnum == 0) {
            $REG_CHANNEL_SQL = ",reg_channel={$channel}";
        }
        try {
            $stmt = $gConn->prepare("UPDATE t_hw_chip_list SET dev_regnum=dev_regnum+1, dev_cpu=?, dev_gpu=?, dev_android=?, dev_ram=?, dev_rom=?, dev_hdmi=?, dev_screen_w=?, dev_screen_h=?, lastregip=?, last_channel=? $REG_CHANNEL_SQL WHERE macid=?");
            $stmt->bind_param(
                'sssssssssis',
                $dev_cpu,
                $dev_gpu,
                $dev_android,
                $dev_ram,
                $dev_rom,
                $dev_hdmi,
                $dev_screen_w,
                $dev_screen_h,
                $lMyIP,
                $channel,
                $real_macid
            );
            $stmt->execute();
            $stmt->close();
        } catch (\Throwable $th) {
            output_json_db_error($response);
            $gConn->rollback();
            Log::DEBUG("Error sql: " . $lSQL);
            Log::DEBUG("Update chip list exception: " . $th);
            Log::DEBUG("Update chip list error: " . mysqli_error($gConn));
            Log::DEBUG("Register info: " . json_encode($_POST));
            exit;
        }
        try {
            $stmt = $gConn->prepare("INSERT INTO t_hw_chip_bind_record (macid, ip, channel, dev_cpu, dev_gpu, dev_android, dev_ram, dev_rom, dev_hdmi, dev_screen_w, dev_screen_h) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
            $stmt->bind_param(
                'ssissssssss',
                $real_macid,
                $lMyIP,
                $channel,
                $dev_cpu,
                $dev_gpu,
                $dev_android,
                $dev_ram,
                $dev_rom,
                $dev_hdmi,
                $dev_screen_w,
                $dev_screen_h
            );
            $stmt->execute();
            $stmt->close();
        } catch (\Throwable $th) {
            output_json_db_error($response);
            $gConn->rollback();
            Log::DEBUG("Error sql: " . $lSQL);
            Log::DEBUG("Insert chip bind record exception: " . $th);
            Log::DEBUG("Insert chip bind record error: " . mysqli_error($gConn));
            Log::DEBUG("Register info: " . json_encode($_POST));
            exit;
        }
    }
}

$register_date =  date("Y-m-d H:i:s");
try {
    $stmt = $gConn->prepare("INSERT INTO t_all_gameusers (name, nickname, icon_url, register_date, ip, reg_ip_country, reg_region, server_region_idx, reg_channel, macid, app_type, last_province, last_city, last_login_ip) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    $stmt->bind_param(
        'sssssiiiisisss',
        $name,
        $nickname,
        $icon_url,
        $register_date,
        $lMyIP,
        $lRegIPCID,
        $region_id,
        $SERVER_REGION_CUR_IDX,
        $channel,
        $real_macid,
        $GLOBAL_MATCH_APP,
        $last_province,
        $last_city,
        $lMyIP
    );
    $stmt->execute();
    $stmt->close();
} catch (\Throwable $th) {
    output_json_db_error($response);
    $gConn->rollback();
    Log::DEBUG("Error sql: " . $lSQL);
    Log::DEBUG("Insert gameusers exception: " . $th);
    Log::DEBUG("Insert gameusers error: " . mysqli_error($gConn));
    Log::DEBUG("Register info: " . json_encode($_POST));
    exit;
}

$result = $gConn->query("SELECT user_id,icon_url,reg_region FROM t_all_gameusers WHERE name='{$name}'");
if (!$result) {
    output_json_db_error($response);
    $gConn->rollback();
    exit;
} else {
    $row = $result->fetch_assoc();
    $response->user_id =  intval($row['user_id']);
    $response->icon_url =  $row['icon_url'];
    $response->region_id =  intval($row['reg_region']);
    $simple_user_id = $response->user_id;
    if (isset($SERVER_USER_IDX[$SERVER_REGION_CUR_IDX])) {
        $simple_user_id = $simple_user_id - $SERVER_USER_IDX[$SERVER_REGION_CUR_IDX] + ($SERVER_USER_IDX[$SERVER_REGION_CUR_IDX] / 100000);
    }

    $nickname =  'Player_' . $simple_user_id;
    $name =  $lAES->encrypt('Guest' . $response->user_id);

    $result2 = $gConn->query("UPDATE t_all_gameusers SET name='{$name}',nickname='{$nickname}',ptype=0 WHERE user_id=" . $response->user_id . " ");
    if (!$result2) {
        output_json_db_error($response);
        $gConn->rollback();
        exit;
    }
    $response->nickname = $nickname;
    $result2 = $gConn->query("UPDATE t_hw_chip_list SET user_id={$response->user_id},user_reg_time=now() WHERE macid='{$real_macid}' ");
    if (!$result2) {
        output_json_db_error($response);
        $gConn->rollback();
        exit;
    }
}

// 建立背包
$gResult = $gConn->query("INSERT INTO t_hw_users_package (user_id) VALUES ({$response->user_id})");
if (!$gResult) {
    output_json_db_error($response);
    $gConn->rollback();
    exit;
}

$gConn->commit();

//-------------------------------------------------------------------------------------------
//make new beibao
// $result_x = $gConn->query("INSERT INTO t_hw_gameusers_beibao (userid, coins, leiji_coins, max_coins, touxiang_card_num, name_card_num, clear_score_card_num, jinyan_card_num,jingyan_baohu_card_num, laba_num, touxiang, biaoqing, touxiang_using,jingyan_baohu_num_left, chat_color_id, vip_class, phone_num, huanledou, youxibi, df_touxiang_id, baoyue_gap, tili, free_tl_date, free_tl_num, qd_date,qd_day_num) VALUES (" . $response->userid . ", 0, 0, 0, 0, 0, 0, 0, 0, 0,'', '', 0, 0, 0, 0, '', 0, 0, 0, '2017-01-01', 7, '2017-01-01', 0, '2017-01-01', 0) ");

//make extra info
$gConn->query("INSERT INTO t_all_gameusers_extra_info (user_id) VALUES ({$response->user_id}) ");
//-------------------------------------------------------------------------------------------

//new login token
$response->login_token = get_new_login_token($gConn, $response->user_id, $channel);


header('Content-type: application/json');
echo json_encode($response);

$gConn->close();


function recordRegisterInfo()
{
    Log::DEBUG("Register info: " . json_encode($_POST));
}
