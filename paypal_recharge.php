<?php

include(dirname(__FILE__) . "/games_funs.php");
include(dirname(__FILE__) . '/vendor/autoload.php');
require_once './log.php';

//初始化日志
$logHandler = new CLogFileHandler(sys_get_temp_dir() . DIRECTORY_SEPARATOR . "hw_paypal_users_order.log");
$log = Log::Init($logHandler, 1);

class GLResponse
{
    public $msg = 'ok';
    public $ret = 0;

    public $pay_url = ''; //支付链接
    public $trade_no = ''; //订单号
};


$response = new GLResponse();
//-----------------------------------------------------------

$user_id = $_POST['user_id'];
$login_token = $_POST['login_token'];
$product_id = $_POST['device_id'];
$lang = $_POST['lang'];
$channel = $_POST['channel'];
$recharge_id = $_POST['recharge_id'];
$token = $_POST['token'];

if ($user_id == NULL || $login_token == NULL || $product_id == NULL || $lang == NULL || $channel == NULL || $recharge_id == NULL || $token == NULL) {
    output_json_para_error($response);
    exit;
}

$user_id = intval($user_id);
$lang = intval($lang);
$channel = intval($channel);
$recharge_id = intval($recharge_id);
$product_id = trim($product_id);
$login_token = trim($login_token);
$token = trim($token);

if (sha1($user_id . $product_id . $lang . $channel . $recharge_id . $VS_KEY) != $token) {
    output_json_token_error($response);
    exit;
}

$language_id = $gDLangTSuffix;
if (isset($gAllLanguage[$lang])) {
    $language_id = $lang;
}

//-----------------------------------------------------------

$gConn = db_connect();

if (!$gConn) {
    output_json_db_error($response);
    exit;
}

$product_id = mysqli_real_escape_string($gConn, $product_id);

if (!is_login_token_valid($gConn, $response, $user_id, $login_token)) {
    exit;
}

$real_chip = '';
$gResult = $gConn->query("SELECT macid FROM t_hw_chip_list WHERE macid_enc='{$product_id}'");
if (!$gResult) {
    output_json_db_error($response);
    exit;
} else {
    $result_num = $gResult->num_rows;

    if ($result_num != 1) {
        output_json_api_error($response, -2, 'Chip id error!');
        exit;
    }

    $row = $gResult->fetch_assoc();
    $real_chip = $row['macid'];

    $gResult->free();
}

$gResult = $gConn->query("SELECT channel FROM t_hw_all_channel WHERE channel={$channel}");
if (!$gResult) {
    output_json_db_error($response);
    exit;
} else {
    $lNum = $gResult->num_rows;

    if ($lNum <= 0) {
        output_json_api_error($response, -4, 'channel error!');
        exit;
    }
}

// 查询当前货币种类
$country_code = 0;
$gResult = $gConn->query("SELECT ptype,reg_ip_country FROM t_all_gameusers WHERE user_id={$user_id}");
if (!$gResult) {
    output_json_db_error($response);
    exit;
} else {
    $result_num = $gResult->num_rows;

    if ($result_num == 1) {
        $row = $gResult->fetch_assoc();

        $country_code = intval($row['reg_ip_country']);
        $ptype = intval($row['ptype']);

        if ($ptype != 1 && $ptype != 2) {
            output_json_api_error($response, -8, 'guest user cant recharge!');
            exit;
        }
    }

    $gResult->free();
}
$currency_id = 0;
$pay_currency_code = 'USD';
$currency_symbol = '';
// $gResult = $gConn->query("SELECT pay_currency_code FROM t_hw_country_list WHERE id={$country_code}");
// if (!$gResult) {
//     output_json_db_error($response);
//     exit;
// } else {
//     $result_num = $gResult->num_rows;

//     if ($result_num == 1) {
//         $row = $gResult->fetch_assoc();

//         $pay_currency_code = trim($row['pay_currency_code']);
//     }

//     $gResult->free();
// }

if (isset($gAllCurrency[$pay_currency_code])) {
    $currency_id = $gAllCurrency[$pay_currency_code][0];
    $currency_symbol = $gAllCurrency[$pay_currency_code][1];
}


$pay_id = 0;
$tickets = 0;
$lj_tickets = 0;
$price = 0;
$trade_no = '';
$gResult = $gConn->query("SELECT id,currency_list,price_list,base_tickets,tickets,lj_tickets FROM t_hw_recharge_products WHERE isshow=1 AND id={$recharge_id} ");
if (!$gResult) {
    output_json_db_error($response);
    exit;
} else {
    $lNum = $gResult->num_rows;

    if ($lNum == 0) {
        output_json_api_error($response, -5, 'recharge error!');
        exit;
    }

    $row = $gResult->fetch_assoc();

    $pay_id = intval($row['id']);
    $tickets = intval($row['tickets']);
    $lj_tickets = intval($row['lj_tickets']);
    $lCurrencyArray = array_flip(explode('+', trim($row['currency_list'])));
    $lPriceArray = explode('+', trim($row['price_list']));
    if (isset($lCurrencyArray[$pay_currency_code])) {
        $price = intval($lPriceArray[$lCurrencyArray[$pay_currency_code]]);
        $trade_no = 'pp' . $user_id . date("YmdHis") . $pay_id;
    } else {
        output_json_api_error($response, -5, 'recharge error!');
        exit;
    }

    $gResult->free();
}

$paypal_price = $price / 100;

$item_name = 'HandGame_recharge';

// get paypal token
$paypalAccessToken = "";
$gResult = $gConn->query("SELECT token_type,access_token FROM t_hw_paypal_access_token ORDER BY id DESC LIMIT 1");
if (!$gResult) {
    output_json_db_error($response);
    exit;
} else {
    $lNum = $gResult->num_rows;

    if ($lNum > 0) {
        $row = $gResult->fetch_assoc();

        $token_type = $row['token_type'];
        $access_token = $row['access_token'];
        $paypalAccessToken = "$token_type $access_token";
    } else {
        output_json_api_error($response, -6, 'paypal token error!');
        exit;
    }

    $gResult->free();
}

// create order
$now_time = date('Y-m-d H:i:s');
$gResult = $gConn->query("INSERT INTO t_hw_recharge_trade_no (user_id,pay_id,pay_channel,currency,price,tickets,lj_tickets,trade_no,state,qudao_id,chip_id) VALUES ({$user_id}, {$pay_id}, {$PAYPAL_TYPE}, '{$pay_currency_code}', {$price}, {$tickets}, {$lj_tickets}, '{$trade_no}', 0, {$channel}, '{$real_chip}')");
if (!$gResult) {
    output_json_db_error($response);
    exit;
}

$createPaypalOrderBody = array(
    "intent" => "CAPTURE",
    "purchase_units" => array(
        array(
            "reference_id" => $trade_no,
            "amount" => array(
                "currency_code" => $pay_currency_code,
                "value" => "$paypal_price",
            ),
            "invoice_id" => $trade_no,
        )
    ),
    "payment_source" => array(
        "paypal" => array(
            "experience_context" => array(
                "payment_method_preference" => "IMMEDIATE_PAYMENT_REQUIRED",
                "brand_name" => "Global Match",
                "locale" => $gAllLanguage[$language_id],
                "landing_page" => "NO_PREFERENCE",
                "shipping_preference" => "NO_SHIPPING",
                "user_action" => "PAY_NOW",
                "return_url" => $PAYPAL_RETURN_URL,
                "cancel_url" => $PAYPAL_DEFAULT_PAGE
            )
        )
    )
);


$maxRetries = 3; // 最大重试次数
$attempt = 0; // 当前重试次数
while ($attempt < $maxRetries) {
    try {
        $paypalCreateOrderRequest = WpOrg\Requests\Requests::post($PAYPAL_REST_URL . '/v2/checkout/orders', array("Content-Type" => "application/json", "Authorization" => $paypalAccessToken, "PayPal-Request-Id" => $trade_no), json_encode($createPaypalOrderBody), array("timeout" => 30, "connect_timeout" => 30));
        if ($paypalCreateOrderRequest->status_code == 200) {
            $paypalCreateOrderData = json_decode($paypalCreateOrderRequest->body);
            $response->pay_url = $paypalCreateOrderData->links[1]->href;
            $response->trade_no = $trade_no;
            if (isset($paypalCreateOrderData->id)) {
                $gConn->query("UPDATE t_hw_recharge_trade_no SET business_order='{$paypalCreateOrderData->id}' WHERE trade_no='{$trade_no}'");
            }
        } else {
            Log::Debug("create order error, status_code: $paypalCreateOrderRequest->status_code, $paypalCreateOrderRequest->body");
            output_json_register_error2($response, -7, 'Paypal request error.');
            exit;
        }
        break;
    } catch (WpOrg\Requests\Exception $e) {
        Log::Debug("Request exception: " . $e);

        // 重发请求
        $attempt++;
        if ($attempt == $maxRetries) {
            Log::Debug("Request limit.");
            output_json_register_error2($response, -7, 'Paypal request error.');
            exit;
        }
    }
}



header('Content-type: application/json');
echo json_encode($response);

$gConn->close();
