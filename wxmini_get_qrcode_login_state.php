<?php

include(dirname(__FILE__) . "/games_funs.php");
require_once(dirname(__FILE__) . "/GeoIP2/geoip2.phar");

use GeoIp2\Database\Reader;


class GLResponse
{

    public $msg = 'ok';
    public $ret = 0;

    public $user_id = 0;
    public $nickname = '';
    public $icon_url = '';
    public $login_token = '';
    public $is_new = 1;
    public $ptype = 1; //0->游客,1->正常用户,2->知名玩家,3->机器人


    public $state = 0;
};

$response = new GLResponse();

//-----------------------------------------------------------
$chip_id = $_POST['device_id'];
$series_no = $_POST['series_no'];
$token = $_POST['token'];

if ($chip_id == NULL || $series_no == NULL || $token == NULL || $chip_id <= 0) {
    output_json_para_error($response);
    exit;
}

$chip_id = trim($chip_id);
$series_no = trim($series_no);
$token = trim($token);

if (sha1($chip_id . $series_no . $VS_KEY) != $token) {
    output_json_token_error($response);
    exit;
}

//connect database and query
$gConn = db_connect();

if (!$gConn) {
    output_json_db_error($response);
    exit;
}

$last_province = '未知';
$last_city = '未知';

$myip = get_ip();
try {
    $reader = new Reader('./GeoIP2/GeoLite2-City.mmdb');
    $record = $reader->city($myip);

    $lRegIPCode = trim($record->country->isoCode);

    if ($lRegIPCode == 'CN') {
        if (isset($record->city) && isset($record->city->names) && isset($record->city->names['zh-CN'])) {
            $last_city = str_replace('市', '', $record->city->names['zh-CN']);
        }
        if (isset($record->subdivisions) && isset($record->subdivisions[0]) && isset($record->subdivisions[0]->names) && isset($record->subdivisions[0]->names['zh-CN'])) {
            $last_province = $record->subdivisions[0]->names['zh-CN'];
        } else {
            $last_province = '中国';
        }
    } else {
        if (isset($record->country) && isset($record->country->names) && isset($record->country->names['zh-CN'])) {
            $last_province = $record->country->names['zh-CN'];
        }
    }
} catch (\Throwable $th) {
    $lRegIPCID = 211;
    $last_province = '中国';
}

$real_chipid = '';
$result = $gConn->query("SELECT macid FROM t_hw_chip_list WHERE macid_enc='" . $chip_id . "'");
if (!$result) {
    output_json_db_error($response);
    exit;
} else {
    $result_num = $result->num_rows;

    if ($result_num == 0) {
        output_json_register_error2($response, -2, 'chip_id not exist!');
        exit;
    }

    $row = $result->fetch_assoc();
    $real_chipid = $row['macid'];
}

$result = $gConn->query("SELECT state,login_user,login_token,isnew FROM t_wxmini_qrcode WHERE series_no = '" . $series_no . "' AND chip_id = '" . $real_chipid . "'");
if (!$result) {
    output_json_db_error($response);
    exit;
} else {
    $result_num = $result->num_rows;

    if ($result_num == 0) {
        output_json_register_error2($response, -3, 'series_no not exist or expire!');
        exit;
    }

    $row = $result->fetch_assoc();
    $state = intval($row['state']);

    if ($state == 1) {
        $login_user = intval($row['login_user']);
        $login_token = $row['login_token'];

        $result2 = $gConn->query("SELECT user_id,nickname,icon_url FROM t_all_gameusers WHERE user_id = " . $login_user);
        if (!$result2) {
            output_json_db_error($response);
            exit;
        } else {
            $result_num2 = $result2->num_rows;

            if ($result_num2 == 0) {
                output_json_register_error2($response, -3, 'login_user not exist!');
                exit;
            }

            $row2 = $result2->fetch_assoc();
            $response->user_id = intval($row2['user_id']);
            $response->nickname = $row2['nickname'];
            $response->icon_url = $row2['icon_url'];
            $response->is_new = intval($row['isnew']);


            // 结束二维码登录
            $gConn->query("UPDATE t_wxmini_qrcode SET state = 2 WHERE series_no = '" . $series_no . "' AND chip_id = '" . $real_chipid . "'");

            // 更新用户登录信息
            $gConn->query("UPDATE t_all_gameusers SET last_login_ip = '" . $myip . "',last_province = '" . $last_province . "',last_city = '" . $last_city . "' WHERE user_id = " . $login_user);

            //new login token
            $response->login_token = get_new_login_token($gConn, $response->user_id, 0);
        }

        $result2->free();
    } else if ($state == 2) {
        output_json_register_error2($response, -3, 'series_no not exist or expire!');
        exit;
    }

    $response->state = $state;

    $result->free();
}


header('Content-type: application/json');
echo json_encode($response);

$gConn->close();
