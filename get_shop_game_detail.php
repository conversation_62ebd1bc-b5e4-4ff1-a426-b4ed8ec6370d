<?php

include(dirname(__FILE__) . "/games_funs.php");

class GLResponse
{
    public $msg = 'ok';
    public $ret = 0;

    public $gameInfo = NULL;
};

class GameData
{
    public $game_id = 0;
    public $series_id = 0;
    public $series_name = '';
    public $game_main_icon = '';
    public $game_screenshot = NULL;
    public $game_name = '';
    public $game_desc = '';
    public $downloads = 0;
    public $developer = '';
    public $net_type = 0;
    public $origin_price = 0;
    public $price = 0;
    public $added = 0;
    public $DLDataList = NULL;
}

class DLData
{
    public $file_size = 0;
    public $file_crc = '';
    public $file_url = '';
    public $file_name = '';
    public $file_suffix = '';
    public $platform = NULL;
};

class AllDLData
{
    public $rom = NULL;
    public $init = NULL;
    public $practiceRom = NULL;
    public $biosRom = NULL;
    public $parentRom = NULL;
};

class RomPlatform
{
    public $id = 0;
    public $type = '';
};

$response = new GLResponse();
//-----------------------------------------------------------

$user_id = $_GET['user_id'];
$login_token = $_GET['login_token'];
$product_id = $_GET['device_id'];
$lang = $_GET['lang'];
$channel = $_GET['channel'];
$game_id = $_GET['game_id'];
$token = $_GET['token'];

if ($user_id == NULL || $login_token == NULL || $product_id == NULL || $lang == NULL || $channel == NULL || $game_id == NULL || $token == NULL) {
    output_json_para_error($response);
    exit;
}

$user_id = intval($user_id);
$lang = intval($lang);
$channel = intval($channel);
$game_id = intval($game_id);
$product_id = trim($product_id);
$login_token = trim($login_token);
$token = trim($token);

if (sha1($user_id . $product_id . $lang . $channel . $game_id . $VS_KEY) != $token) {
    output_json_token_error($response);
    exit;
}

$language_id = $gDLangTSuffix;
if (isset($gAllLanguage[$lang])) {
    $language_id = $lang;
}

//-----------------------------------------------------------

$gConn = db_connect();

if (!$gConn) {
    output_json_db_error($response);
    exit;
}

$product_id = mysqli_real_escape_string($gConn, $product_id);

if (!is_login_token_valid($gConn, $response, $user_id, $login_token)) {
    exit;
}

$lMyGamesArray = [];

$gResult = $gConn->query("SELECT games FROM t_yzvs_users_games WHERE user_id = {$user_id}");
if (!$gResult) {
    output_json_db_error_with_msg($response, mysqli_error($gConn));
    exit();
} else {

    $lNum = $gResult->num_rows;

    if ($lNum > 0) {
        $lRow = $gResult->fetch_assoc();
        $lGames = trim($lRow['games']);
        if (strlen($lGames) > 0) {
            $lMyGamesArray = array_flip(explode(',', $lGames));
        }
    }

    $gResult->free();
}

$lMyGamesCnt = COUNT($lMyGamesArray);
$gResult = $gConn->query("SELECT ta.game_id,ta.series_id,ta.main_name,ta.game_text,ta.dev_desc,ta.net_type,ta.game_main_icon,ta.game_screenshot,ta.origin_price,ta.price,ta.downloads,tb.main_name_t,tb.game_text_t FROM t_yzvs_all_games_common AS ta INNER JOIN t_yzvs_all_games_common_lang_{$language_id} AS tb ON ta.game_id=tb.game_id WHERE ta.game_id={$game_id} AND ta.is_show=1 ");
if (!$gResult) {
    output_json_db_error_with_msg($response, mysqli_error($gConn));
    exit();
} else {
    $lNum = $gResult->num_rows;

    if ($lNum > 0) {
        $lRow = $gResult->fetch_assoc();

        $lGame = new GameData();
        $lGame->game_id = intval($lRow['game_id']);
        if ($lRow['main_name_t'] != NULL) {
            $lGame->game_name = trim($lRow['main_name_t']);
        } else {
            $lGame->game_name = trim($lRow['main_name']);
        }
        if ($lRow['game_text_t'] != NULL) {
            $lGame->game_desc = trim($lRow['game_text_t']);
        } else {
            $lGame->game_desc = trim($lRow['game_text']);
        }
        if ($lRow['game_main_icon'] != NULL) {
            $lGame->game_main_icon = $SERVER_RES_PREFIX[$SERVER_REGION_CUR_IDX] . $YZVS_GAME_ICON_PREFIX . trim($lRow['game_main_icon']);
        }
        if ($lRow['game_screenshot'] != NULL) {
            $game_screenshot = explode(";", trim($lRow['game_screenshot']));
            for ($i = 0; $i < count($game_screenshot); $i++) {
                $game_screenshot[$i] = $SERVER_RES_PREFIX[$SERVER_REGION_CUR_IDX] . $YZVS_GAME_ICON_PREFIX . trim($game_screenshot[$i]);
            }
            $lGame->game_screenshot = $game_screenshot;
        }
        $lGame->developer = trim($lRow['dev_desc']);
        $lGame->net_type = intval($lRow['net_type']);
        $lGame->origin_price = intval($lRow['origin_price']);
        $lGame->price = intval($lRow['price']);
        $lGame->downloads = intval($lRow['downloads']);
        if ($lMyGamesCnt > 0 && isset($lMyGamesArray[$lGame->game_id])) {
            $lGame->added = 1;
        }
        $lGame->series_id = intval($lRow['series_id']);

        $lResult = $gConn->query("SELECT ta.series_id,ta.series_name,tb.series_name_t FROM t_yzvs_all_game_series AS ta INNER JOIN t_yzvs_all_game_series_lang_{$language_id} AS tb ON ta.series_id=tb.series_id WHERE ta.series_id={$lGame->series_id}");
        if (!$lResult) {
            output_json_db_error_with_msg($response, mysqli_error($gConn));
            exit();
        } else {
            $lNum2 = $lResult->num_rows;
            if ($lNum2 > 0) {
                $lRow2 = $lResult->fetch_assoc();

                if ($lRow2['series_name_t'] != NULL) {
                    $lGame->series_name = trim($lRow2['series_name_t']);
                } else {
                    $lGame->series_name = trim($lRow2['series_name']);
                }
            }

            $lResult->free();
        }

        if ($lGame->added == 1) {
            $lGame->DLDataList = new AllDLData();

            $lResult = $gConn->query("SELECT rom_available,rom_file_size,rom_file_crc,rom_file_url_1,rom_file_url_2,rom_real_name,rom_real_suffix FROM t_yzvs_all_games_dldata  WHERE game_id={$game_id}");
            if (!$lResult) {
                output_json_db_error_with_msg($response, mysqli_error($gConn));
                exit();
            } else {
                $lNum2 = $lResult->num_rows;

                if ($lNum2 > 0) {
                    $lRow2 = $lResult->fetch_assoc();

                    $lDLData = new DLData();
                    $lDLData->file_size = intval($lRow2['rom_file_size']);
                    $lDLData->file_crc = trim($lRow2['rom_file_crc']);
                    $lDLData->file_name = trim($lRow2['rom_real_name']);
                    $lDLData->file_suffix = trim($lRow2['rom_real_suffix']);
                    if ($lRow2['rom_file_url_1'] != NULL) {
                        $lDLData->file_url = $SERVER_RES_PREFIX[$SERVER_REGION_CUR_IDX] . $YZVS_ROM_ICON_PREFIX . trim($lRow2['rom_file_url_1']);
                    }
                    $rom_available = trim($lRow2['rom_available']);
                    if ($rom_available != NULL) {
                        $lResult2 = $gConn->query("SELECT id,plat_name FROM t_hw_all_rom_platform  WHERE id IN ($rom_available)");
                        if (!$lResult2) {
                            output_json_db_error_with_msg($response, mysqli_error($gConn));
                            exit();
                        } else {
                            $lNum3 = $lResult2->num_rows;

                            for ($j = 0; $j < $lNum3; $j++) {
                                $lRow3 = $lResult2->fetch_assoc();

                                $lRomPlat = new RomPlatform();
                                $lRomPlat->id = intval($lRow3['id']);
                                $lRomPlat->type = trim($lRow3['plat_name']);

                                $lDLData->platform[] = $lRomPlat;
                            }

                            $lResult2->free();
                        }
                    }

                    $lGame->DLDataList->rom = $lDLData;
                }

                $lResult->free();
            }
        }

        $response->gameInfo = $lGame;
    }

    $gResult->free();
}

header('Content-type: application/json');
echo json_encode($response);

$gConn->close();
