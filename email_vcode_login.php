<?php

include(dirname(__FILE__) . "/games_funs.php");

//---------------- JSON DATA CLASS ---------------------------

class GLResponse
{

    public $msg = 'ok';
    public $ret = 0;

    public $user_id = 0; //用户ID

    public $name = ''; //账号（Email）
    public $nickname = ''; //昵称
    public $icon_url = ''; //头像URL
    public $gold = 0; //剩余金币

    public $server_region_idx = -1; //大区服务器ID(注册时选定，后期不能更改)
    public $abode_country = -1; //居住地国家ID(注册时选定，后期不能更改)
    // public $real_country = -1;//国籍
    public $region_id = -1; //区域ID
    public $is_new = 0; //
    public $ptype = 1; //0->游客,1->正常用户,2->知名玩家,3->机器人    

    public $login_token = ''; //登录token
    public $cur_server_region_idx = -1; //当前大区服务器ID,无异常时server_region_idx与其相同，有异常则不同

};

$response = new GLResponse();

$name = $_POST['name']; //email
$email_no = $_POST['email_no'];
$email_code = $_POST['email_code'];
$product_id = $_POST['device_id'];
$channel = $_POST['channel'];
$token = $_POST['token'];

$dev_cpu = $_POST['dev_cpu'];
$dev_gpu = $_POST['dev_gpu'];
$dev_android = $_POST['dev_android'];
$dev_ram = $_POST['dev_ram'];
$dev_rom = $_POST['dev_rom'];
$dev_hdmi = $_POST['dev_hdmi'];
$dev_screen_w = $_POST['dev_screen_w'];
$dev_screen_h = $_POST['dev_screen_h'];

if (NULL == $name || NULL == $email_no || NULL == $email_code || NULL == $product_id || NULL == $channel || NULL == $token || NULL == $product_id || NULL == $dev_cpu || NULL == $dev_gpu || NULL == $dev_android || NULL == $dev_ram || NULL == $dev_rom || NULL == $dev_hdmi || NULL == $dev_screen_w || NULL == $dev_screen_h) {
    output_json_para_error($response);
    exit;
}

$name = trim($name);
$email_no = trim($email_no);
$email_code = trim($email_code);
$product_id = trim($product_id);
$channel = intval($channel);
$token = trim($token);

$dev_cpu = trim($dev_cpu);
$dev_gpu = trim($dev_gpu);
$dev_android = trim($dev_android);
$dev_ram = trim($dev_ram);
$dev_rom = trim($dev_rom);
$dev_hdmi = trim($dev_hdmi);
$dev_screen_w = trim($dev_screen_w);
$dev_screen_h = trim($dev_screen_h);

if (0 == strlen($name) || 0 == strlen($email_no) || 0 == strlen($email_code) || 0 == strlen($product_id)) {
    output_json_para_error($response);
    exit;
}

if (sha1($name . $email_no . $email_code . $product_id . $channel . $VS_KEY) != $token) {
    output_json_token_error($response);
    exit;
}

if (!isValidEmailAddr($name)) {
    output_json_api_error($response, -3, 'It isn`t a valid Email!');
    exit;
}

//connect database and query
$gConn = db_connect();

if (!$gConn) {
    output_json_db_error($response);
    exit;
}

$name = mysqli_real_escape_string($gConn, $name);
$email_no = mysqli_real_escape_string($gConn, $email_no);
$email_code = mysqli_real_escape_string($gConn, $email_code);
$product_id = mysqli_real_escape_string($gConn, $product_id);

$r_dev_regnum = 0;
$r_dev_cpu = '';
$r_dev_gpu = '';
$r_dev_android = '';
$r_dev_ram = '';
$r_dev_rom = '';
$r_dev_hdmi = '';
$r_dev_screen_w = '';
$r_dev_screen_h = '';

$real_macid = '';

$gResult = $gConn->query("SELECT macid,dev_regnum,dev_cpu,dev_gpu,dev_android,dev_ram,dev_rom,dev_hdmi,dev_screen_w,dev_screen_h FROM t_hw_chip_list WHERE macid_enc='{$product_id}'");

if (!$gResult) {
    output_json_db_error($response);
    exit;
} else {

    $result_num = $gResult->num_rows;

    if ($result_num < 1) {
        Log::DEBUG('CHIP ID ERROR:' . $product_id);
        output_json_api_error($response, -2, 'chip id error!');
        exit;
    } else {
        $row = $gResult->fetch_assoc();

        $real_macid = trim($row['macid']);
        $r_dev_regnum = intval($row['dev_regnum']);
        $r_dev_cpu = $row['dev_cpu'];
        $r_dev_gpu = $row['dev_gpu'];
        $r_dev_android = $row['dev_android'];
        $r_dev_ram = $row['dev_ram'];
        $r_dev_rom = $row['dev_rom'];
        $r_dev_hdmi = $row['dev_hdmi'];
        $r_dev_screen_w = $row['dev_screen_w'];
        $r_dev_screen_h = $row['dev_screen_h'];
    }
    $gResult->free();
}

$lMyIP = get_ip();

$lAES = new AESSSL($EMAILAES128KEY);

$lEncName = $lAES->encrypt($name);

$lNowTime = date('Y-m-d H:i:s');
$lCMD = "SELECT email_no,email,code,state,channel,try_num,TIMESTAMPDIFF(MINUTE, `time`, '{$lNowTime}') AS mins FROM t_hw_email_verify_no WHERE email_no =  ? ";
$lStmt = $gConn->prepare($lCMD); //mysql client --> mysql server(network time)???
if (!$lStmt) {
    output_json_db_error($response);
    exit;
} else {
    $lStmt->bind_param("s", $email_no);

    if (!$lStmt->execute()) { //mysql client --> mysql server(network time)???
        output_json_db_error($response);
        exit;
    } else {
        $lResult = $lStmt->get_result();

        $lNum = $lResult->num_rows;
        if (0 == $lNum) {
            output_json_api_error($response, -4, 'email_no error');
            exit;
        }
        $row = $lResult->fetch_assoc();
        $email_no = $row['email_no'];
        $lEmail =  $row['email'];
        $lCode =  $row['code'];
        $lState =  intval($row['state']);
        $lChannel = intval($row['channel']);
        $lTryNum =  intval($row['try_num']);
        $lMins =  intval($row['mins']);
        if ($lChannel != $channel) {
            output_json_api_error($response, -10, 'channel error!');
            exit;
        }
        if ($lEmail != $lEncName) {
            output_json_api_error($response, -5, 'account and email do not match');
            exit;
        }
        if (0 != $lState || $lMins > $EMAILCODEVALIDMINS || $lTryNum >= $EMAILCODEERRORCNT) {
            output_json_api_error($response, -6, 'verification code expired');
            exit;
        }
        if ($lCode != $email_code) {
            $gConn->query("UPDATE t_hw_email_verify_no SET try_num=try_num+1 WHERE email_no = '" . $email_no . "'");
            output_json_api_error($response, -7, 'verification code error');
            exit;
        }
        $gConn->query("UPDATE t_hw_email_verify_no SET state=1 WHERE email_no = '" . $email_no . "'");

        $lResult->free();
    }
    $lStmt->close();
}

$gResult = $gConn->query("SELECT channel FROM t_hw_all_channel WHERE channel={$channel}");
if (!$gResult) {
    output_json_db_error($response);
    exit;
} else {
    $lNum = $gResult->num_rows;

    if ($lNum != 1) {
        output_json_api_error($response, -10, 'channel error!');
        exit;
    }

    $gResult->free();
}

if ($r_dev_cpu != $dev_cpu || $r_dev_gpu != $dev_gpu || $r_dev_android != $dev_android || $r_dev_ram != $dev_ram || $r_dev_rom != $dev_rom || $r_dev_hdmi != $dev_hdmi || $r_dev_screen_w != $dev_screen_w || $r_dev_screen_h != $dev_screen_h) {
    if ($r_dev_regnum >= $DEVICE_REG_LIMIT) {
        output_json_api_error($response, -8, 'Maximum number of bindings');
        exit;
    } else {

        $gConn->begin_transaction();

        $REG_CHANNEL_SQL = '';
        if ($r_dev_regnum == 0) {
            $REG_CHANNEL_SQL = ",reg_channel={$channel}";
        }

        $gResult = $gConn->query("UPDATE t_hw_chip_list SET dev_regnum=dev_regnum+1,dev_cpu='{$dev_cpu}',dev_gpu='{$dev_gpu}',dev_android='{$dev_android}',dev_ram='{$dev_ram}',dev_rom='{$dev_rom}',dev_hdmi='{$dev_hdmi}',dev_screen_w='{$dev_screen_w}',dev_screen_h='{$dev_screen_h}',lastregip='{$lMyIP}',last_channel={$channel} $REG_CHANNEL_SQL WHERE macid='{$real_macid}'");
        if (!$gResult) {
            output_json_db_error($response);
            $gConn->rollback();
            exit;
        }
        $gResult = $gConn->query("INSERT INTO t_hw_chip_bind_record (macid,ip,channel,dev_cpu,dev_gpu,dev_android,dev_ram,dev_rom,dev_hdmi,dev_screen_w,dev_screen_h) VALUES ('{$real_macid}','{$lMyIP}',{$channel},'{$dev_cpu}','{$dev_gpu}','{$dev_android}','{$dev_ram}','{$dev_rom}','{$dev_hdmi}','{$dev_screen_w}','{$dev_screen_h}')");
        if (!$gResult) {
            output_json_db_error($response);
            $gConn->rollback();
            exit;
        }

        $gConn->commit();
    }
}


//$lCMD = "SELECT * FROM T_snkvs_users WHERE server_region_idx={$SERVER_REGION_CUR_IDX} AND name= ?";//is need ???
$lCMD = "SELECT user_id,icon_url,nickname,reg_region FROM t_all_gameusers WHERE name= ?";
$lStmt = $gConn->prepare($lCMD); //mysql client --> mysql server(network time)???
if (!$lStmt) {
    output_json_db_error($response);
    exit;
} else {
    $lStmt->bind_param("s", $lEncName);

    if (!$lStmt->execute()) { //mysql client --> mysql server(network time)???
        output_json_db_error($response);
        exit;
    } else {
        $lResult = $lStmt->get_result();

        $lNum = $lResult->num_rows;

        if (0 == $lNum) {
            output_json_api_error($response, -9, 'Email don`t exist!');
            exit;
        }
        //fill response
        $row = $lResult->fetch_assoc();

        $response->user_id = intval($row['user_id']);
        $response->name = $name;
        $response->nickname = $row['nickname'];
        $response->icon_url = trim($row['icon_url']);
        $response->region_id = intval($row['reg_region']);
        // $response->abode_country = intval($row['abode_country']);
        // $response->server_region_idx = intval($row['server_region_idx']);

        $lResult->free();
    }

    $lStmt->close();
}

$gResult = $gConn->query("SELECT gold FROM t_hw_users_package WHERE user_id={$response->user_id}");
if (!$gResult) {
    output_json_db_error($response);
    exit;
} else {
    $lNum = $gResult->num_rows;

    if ($lNum > 0) {
        $row = $gResult->fetch_assoc();

        $response->gold = intval($row['gold']);
    } else {
        $gConn->query("INSERT INTO t_hw_users_package (user_id) VALUES ({$response->user_id})");
    }
}

//check server_region_idx
// if ($SERVER_REGION_CUR_IDX != $response->server_region_idx) {
//     output_json_api_error($response, -7, 'server region error!');
//     exit;
// }

//new login token
$response->login_token = get_new_login_token($gConn, $response->user_id, $channel);

//----------------------- START -----------------------------------------

// if (strlen($response->login_token) > 0) {
//     $gConn->close();

//     //connect database and query
//     $gConn = db_connect();

//     if (!$gConn) {
//         output_json_db_error($response);
//         exit;
//     }
//     //Master/Slave ???
//     $lRetryCnt = 6;
//     while ($lRetryCnt--) {

//         $lTokenTable = 't_hw_users_logintoken' . '_' . $SERVER_REGION_NAME_ARRAY[$SERVER_REGION_CUR_IDX];

//         $lResult = $gConn->query("SELECT user_id,login_token FROM {$lTokenTable} WHERE user_id=" . $response->user_id . "  ");

//         if ($lResult) {
//             $result_num_token = $lResult->num_rows;

//             if ($result_num_token > 0) {
//                 $row = $lResult->fetch_assoc();
//                 $r_login_token =  $row['login_token'];
//                 if (trim($response->login_token) == trim($r_login_token)) {
//                     break;
//                 }
//             } else {
//                 //continue;
//             }

//             $lResult->free();
//         }

//         sleep(1);
//     }
// }
//----------------------- END -----------------------------------------


header('Content-type: application/json');
echo json_encode($response);

$gConn->close();
