<?php

include(dirname(__FILE__) . "/../games_funs.php");
include(dirname(__FILE__) . "/../log.php");

include(dirname(__FILE__) . '/../vendor/autoload.php');

//--------------------------------------------------------------------------------

if (!isset($argc) || !isset($argv) || 2 != $argc || '2OWSXYsZCMMU0GLd' != $argv[1]) {
    echo "QUIT";
    exit;
}

//--------------------------------------------------------------------------------

$gConn = db_connect();

if (!$gConn) {
    exit;
}

//初始化日志
$logHandler = new CLogFileHandler(sys_get_temp_dir() . DIRECTORY_SEPARATOR . "hw_paypal_refresh_token.log");
$log = Log::Init($logHandler, 1);

// get paypal token
$paypalAccessToken = "";

$maxRetries = 10; // 最大重试次数
$attempt = 0; // 当前重试次数
while ($attempt < $maxRetries) {
    try {
        $paypalTokenReturnBody = WpOrg\Requests\Requests::post($PAYPAL_REST_URL . '/v1/oauth2/token', array("Content-Type" => "application/x-www-form-urlencoded"), array("grant_type" => "client_credentials"), array("auth" => array($PAYPAL_CLIENDID, $PAYPAL_SECRET), "timeout" => 30, "connect_timeout" => 30));
        if ($paypalTokenReturnBody->status_code == 200) {
            $paypalTokenData = json_decode($paypalTokenReturnBody->body);
            $gResult = $gConn->query("SELECT id FROM t_hw_paypal_access_token ORDER BY id DESC LIMIT 1");
            if ($gResult) {
                $lNum = $gResult->num_rows;

                if ($lNum > 0) {
                    $row = $gResult->fetch_assoc();

                    $id = intval($row['id']);
                    $gConn->query("UPDATE t_hw_paypal_access_token SET app_id='{$paypalTokenData->app_id}',token_type='{$paypalTokenData->token_type}',access_token='{$paypalTokenData->access_token}' WHERE id={$id}");
                } else {
                    $gConn->query("INSERT INTO t_hw_paypal_access_token (app_id,token_type,access_token) VALUES ('{$paypalTokenData->app_id}','{$paypalTokenData->token_type}','{$paypalTokenData->access_token}')");
                }

                $gResult->free();
            }
        } else {
            Log::Debug("error,status_code: $paypalTokenReturnBody->status_code, $paypalTokenReturnBody->body");
            exit;
        }
        break;
    } catch (WpOrg\Requests\Exception $e) {
        // 捕获其他异常，可能需要根据实际情况处理或直接抛出
        Log::Debug("Refresh paypal token exception: " . $e);

        // 重发请求
        $attempt++;
        if ($attempt == $maxRetries) {
            Log::Debug("request limit.");
            break;
        }
    }
}

$gConn->close();

exit;
