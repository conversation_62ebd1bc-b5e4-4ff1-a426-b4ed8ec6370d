# 文件加解密工具

基于 `maesssl.php` 中的 AESSSL 类开发的文件加解密工具，支持单个文件和批量文件的加解密操作。

## 功能特性

- 单个文件加密/解密
- 批量文件夹加密/解密
- 支持多种 AES 加密方式
- 命令行界面支持
- 完整的错误处理和状态反馈
- 支持文件扩展名过滤

## 文件说明

- `maesssl.php` - 原始的 AES 加解密类
- `file_encrypt_decrypt.php` - 文件加解密主程序
- `test_file_encryption.php` - 测试脚本
- `README_file_encryption.md` - 使用说明文档

## 使用方法

### 1. 在 PHP 代码中使用

```php
<?php
require_once 'file_encrypt_decrypt.php';

// 创建加解密实例
$key = 'your-secret-key-16bytes';  // 密钥
$method = 'AES-128-ECB';           // 加密方式
$fileEncrypt = new FileEncryptDecrypt($key, $method);

// 加密文件
$fileEncrypt->encryptFile('input.txt', 'output.txt.encrypted');

// 解密文件
$fileEncrypt->decryptFile('output.txt.encrypted', 'decrypted.txt');

// 批量加密目录（只加密 .txt 文件）
$fileEncrypt->encryptDirectory('./input_dir', './encrypted_dir', 'txt');

// 批量解密目录
$fileEncrypt->decryptDirectory('./encrypted_dir', './decrypted_dir');
?>
```

### 2. 命令行使用

#### 加密单个文件
```bash
php file_encrypt_decrypt.php encrypt <密钥> <输入文件> <输出文件> [加密方式] [IV向量]
```

示例：
```bash
php file_encrypt_decrypt.php encrypt mykey123 test.txt test.txt.encrypted
php file_encrypt_decrypt.php encrypt mykey123 test.txt test.txt.encrypted AES-256-CBC myiv1234567890ab
```

#### 解密单个文件
```bash
php file_encrypt_decrypt.php decrypt <密钥> <输入文件> <输出文件> [加密方式] [IV向量]
```

示例：
```bash
php file_encrypt_decrypt.php decrypt mykey123 test.txt.encrypted test_decrypted.txt
```

#### 批量加密目录
```bash
php file_encrypt_decrypt.php encrypt-dir <密钥> <输入目录> <输出目录> [文件扩展名] [加密方式] [IV向量]
```

示例：
```bash
# 加密所有文件
php file_encrypt_decrypt.php encrypt-dir mykey123 ./input ./output

# 只加密 .txt 文件
php file_encrypt_decrypt.php encrypt-dir mykey123 ./input ./output txt

# 使用 AES-256-CBC 加密
php file_encrypt_decrypt.php encrypt-dir mykey123 ./input ./output txt AES-256-CBC myiv1234567890ab
```

#### 批量解密目录
```bash
php file_encrypt_decrypt.php decrypt-dir <密钥> <输入目录> <输出目录> [加密方式] [IV向量]
```

示例：
```bash
php file_encrypt_decrypt.php decrypt-dir mykey123 ./encrypted ./decrypted
```

## 支持的加密方式

常用的加密方式包括：
- `AES-128-ECB` (默认)
- `AES-128-CBC`
- `AES-192-ECB`
- `AES-192-CBC`
- `AES-256-ECB`
- `AES-256-CBC`

可以通过以下 PHP 代码查看系统支持的所有加密方式：
```php
print_r(openssl_get_cipher_methods());
```

## 密钥和 IV 要求

- **AES-128**: 密钥长度 16 字节，IV 长度 16 字节（CBC 模式需要）
- **AES-192**: 密钥长度 24 字节，IV 长度 16 字节（CBC 模式需要）
- **AES-256**: 密钥长度 32 字节，IV 长度 16 字节（CBC 模式需要）
- **ECB 模式**: 不需要 IV 向量
- **CBC 模式**: 需要 IV 向量

## 测试

运行测试脚本来验证功能：

```bash
php test_file_encryption.php
```

测试脚本会：
1. 创建测试文件
2. 测试单文件加密/解密
3. 验证解密结果的正确性
4. 测试批量加密/解密
5. 验证批量处理结果
6. 清理测试文件

## 注意事项

1. **密钥安全**: 请妥善保管加密密钥，丢失密钥将无法解密文件
2. **文件备份**: 加密前请备份重要文件
3. **密钥长度**: 确保密钥长度符合所选加密方式的要求
4. **IV 向量**: 使用 CBC 模式时需要提供正确长度的 IV 向量
5. **文件权限**: 确保 PHP 有读写相关文件和目录的权限
6. **内存限制**: 大文件加密时注意 PHP 内存限制设置

## 错误处理

程序包含完整的错误处理机制：
- 文件不存在检查
- 文件读写权限检查
- 加密/解密失败检查
- 目录创建失败检查
- 详细的错误信息输出

## 扩展功能

可以根据需要扩展以下功能：
- 支持更多加密算法
- 添加文件完整性校验
- 支持压缩后加密
- 添加进度条显示
- 支持递归目录处理
- 添加配置文件支持
