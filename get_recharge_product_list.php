<?php

include(dirname(__FILE__) . "/games_funs.php");

class GLResponse
{
    public $msg = 'ok';
    public $ret = 0;

    public $rechargeList = NULL;
};

class RechargeItem
{

    public $id = 0;
    public $currency_id = 0;
    public $currency_name = '';
    public $currency_symbol = '';
    public $gold = 0;
    // public $give_gold = 0;
    public $price = 0;
    public $origin_price = 0;
};

$response = new GLResponse();
//-----------------------------------------------------------

$user_id = $_GET['user_id'];
$login_token = $_GET['login_token'];
$product_id = $_GET['device_id'];
$lang = $_GET['lang'];
$channel = $_GET['channel'];
$token = $_GET['token'];

if ($user_id == NULL || $login_token == NULL || $product_id == NULL || $lang == NULL || $channel == NULL || $token == NULL) {
    output_json_para_error($response);
    exit;
}

$user_id = intval($user_id);
$lang = intval($lang);
$channel = intval($channel);
$product_id = trim($product_id);
$login_token = trim($login_token);
$token = trim($token);

if (sha1($user_id . $product_id . $lang . $channel . $VS_KEY) != $token) {
    output_json_token_error($response);
    exit;
}

$language_id = $gDLangTSuffix;
if (isset($gAllLanguage[$lang])) {
    $language_id = $lang;
}


//-----------------------------------------------------------

$gConn = db_connect();

if (!$gConn) {
    output_json_db_error($response);
    exit;
}

$product_id = mysqli_real_escape_string($gConn, $product_id);

if (!is_login_token_valid($gConn, $response, $user_id, $login_token)) {
    exit;
}

// 查询当前货币种类
$country_code = 0;
$gResult = $gConn->query("SELECT reg_ip_country FROM t_all_gameusers WHERE user_id={$user_id}");
if (!$gResult) {
    output_json_db_error($response);
    exit;
} else {
    $result_num = $gResult->num_rows;

    if ($result_num == 1) {
        $row = $gResult->fetch_assoc();

        $country_code = intval($row['reg_ip_country']);
    }

    $gResult->free();
}


$currency_id = 0;
$pay_currency_code = 'USD';
$currency_symbol = '';
// $gResult = $gConn->query("SELECT pay_currency_code FROM t_hw_country_list WHERE id={$country_code}");
// if (!$gResult) {
//     output_json_db_error($response);
//     exit;
// } else {
//     $result_num = $gResult->num_rows;

//     if ($result_num == 1) {
//         $row = $gResult->fetch_assoc();

//         $pay_currency_code = trim($row['pay_currency_code']);
//     }

//     $gResult->free();
// }

if (isset($gAllCurrency[$pay_currency_code])) {
    $currency_id = $gAllCurrency[$pay_currency_code][0];
    $currency_symbol = $gAllCurrency[$pay_currency_code][1];
}

$gResult = $gConn->query("SELECT id,currency_list,price_list,orig_price_list,tickets FROM t_hw_recharge_products WHERE isshow=1 ORDER BY show_sort ASC, tickets ASC");
if (!$gResult) {
    output_json_db_error($response);
    exit;
} else {
    $result_num = $gResult->num_rows;

    for ($i = 0; $i < $result_num; $i++) {
        $row = $gResult->fetch_assoc();

        $lItem = new RechargeItem();

        $lCurrencyArray = array_flip(explode('+', trim($row['currency_list'])));
        $lPriceArray = explode('+', trim($row['price_list']));
        $lOriginPriceArray = explode('+', trim($row['orig_price_list']));
        $lItem->id = intval($row['id']);

        if (isset($lCurrencyArray[$pay_currency_code])) {
            $lItem->price = intval($lPriceArray[$lCurrencyArray[$pay_currency_code]]);
            $lItem->origin_price = intval($lOriginPriceArray[$lCurrencyArray[$pay_currency_code]]);
            $lItem->currency_symbol = $currency_symbol;
            $lItem->currency_id = $currency_id;
            $lItem->currency_name = $pay_currency_code;
            $lItem->gold = intval($row['tickets']);
            $response->rechargeList[] = $lItem;
        }
    }

    $gResult->free();
}

header('Content-type: application/json');
echo json_encode($response);

$gConn->close();
