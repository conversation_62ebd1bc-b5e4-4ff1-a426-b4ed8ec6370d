<?php

class PlayerSubInfo{
    public $userID = 0;
    public $nickname = '';
    public $avatarUrl = '';

    public $netType = 0;
    public $ptype = 0;
    public $rLevel = 0;

    public $city = '未知';
}

class PlayerInfo extends PlayerSubInfo{
    public $userName = '';
}

const TYPE_PINFO_ALL = 1;//PlayerInfo
const TYPE_PINFO_SUB = 2;//PlayerSubInfo

const EXTRA_PINFO_NULL = 0x0;
const EXTRA_PINFO_MUTE = 0x1;
const EXTRA_PINFO_LIVE = 0x2;
const EXTRA_PINFO_BLOCKED = 0x4;

//New
function getPlayerInfo($aConn,$aUserIDOrArray,$aPInfoType,$aNeedExtraPackage,$aNeedExtraInfo){
    $lSingleInfo = is_int($aUserIDOrArray);

    if($lSingleInfo){
        $lUserIDStr = $aUserIDOrArray;
    }else{
        if(0 == COUNT($aUserIDOrArray)) return NULL;

        $lUserIDStr = implode(',',$aUserIDOrArray);
    }

    $gPlayerInfoArray = [];

    $lEPFieldCMD = '';
    $lEPTableCMD = '';
    if($aNeedExtraPackage){
        $lEPFieldCMD = " ,net_type ";
        $lEPTableCMD = " INNER JOIN t_all_gameusers_extra_info AS tb ON ta.user_id=tb.user_id ";
    }
    $lUSERCMD = "
SELECT ta.user_id,name,nickname,icon_url,ptype,r_level,last_province {$lEPFieldCMD}
 FROM t_all_gameusers AS ta 
 {$lEPTableCMD}
 WHERE ta.user_id IN ({$lUserIDStr}) 
";
    $lResult = $aConn->query($lUSERCMD);
    if(!$lResult) 
    {
        return NULL;
    }else{
        $lNum = $lResult->num_rows;
        if(0 == $lNum){
            return NULL;
        }else{
            for($lIDX=0;$lIDX<$lNum;$lIDX++){
                $lRow = $lResult->fetch_assoc();

                $lCurUserID = intval($lRow['user_id']);

                if(isset($gPlayerInfoArray[$lCurUserID]))
                    continue;
                else{
                    
                    if(TYPE_PINFO_ALL == $aPInfoType){
                        $lPlayerInfo = new PlayerInfo();
                        
                        $lPlayerInfo->userName = $lRow['name'];
                        
                    }else{
                        $lPlayerInfo = new PlayerSubInfo(); 
                    }

                    $lPlayerInfo->userID = $lCurUserID;

                    $lPlayerInfo->nickname = $lRow['nickname'];
                    $lPlayerInfo->avatarUrl = $lRow['icon_url'];
                    $lPlayerInfo->ptype = intval($lRow['ptype']);
                    $lPlayerInfo->rLevel = intval($lRow['r_level']);

                    if (isset($lRow['last_province']) && $lRow['last_province'] != NULL && strlen($lRow['last_province']) > 0) {
                        $lPlayerInfo->city = trim($lRow['last_province']);
                    }
        

                    //-------------------------------------------------------------------------------
                    
                    if($aNeedExtraPackage){
                        $lPlayerInfo->netType = intval($lRow['net_type']);
                    }
                    //-------------------------------------------------------------------------------
                    $gPlayerInfoArray[$lCurUserID] = $lPlayerInfo;
                }
            }
        }
        $lResult->free();
    }
    if(EXTRA_PINFO_MUTE == (EXTRA_PINFO_MUTE & $aNeedExtraInfo)){
        //官方禁言
    }
    if(EXTRA_PINFO_LIVE == (EXTRA_PINFO_LIVE & $aNeedExtraInfo)){
        //直播信息
    }
    if(EXTRA_PINFO_BLOCKED == (EXTRA_PINFO_BLOCKED & $aNeedExtraInfo)){
        //官方封号
    }

    if(0 == COUNT($gPlayerInfoArray)){
        return NULL;
    }else{
        if($lSingleInfo){
            if(isset($gPlayerInfoArray[$aUserIDOrArray])){
                return $gPlayerInfoArray[$aUserIDOrArray];
            }else{
                return NULL;
            }
        }else{
            return $gPlayerInfoArray;
        }
    }
}

?>
