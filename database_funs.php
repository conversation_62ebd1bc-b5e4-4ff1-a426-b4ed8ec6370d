<?php

//connect to the game database
function db_connect()
{
    // snkvs db
    /*
  	$result = new mysqli('pc-bp19924gt5701xu55.rwlb.rds.aliyuncs.com', 'xu_snkvs_admin', 'Xiaou$SNKVSDB$2020', 'xiaou_snkvs_new');
  // $result = new mysqli('public-snkvs-database-hz.rwlb.rds.aliyuncs.com', 'xu_snkvs_admin', 'Xiaou$SNKVSDB$2020', 'xiaou_snkvs_new');
*/
    require 'database_config.php';

    $result = new mysqli($DBHOST, $DBUSER, $DBPASS, $DBNAME);

    if ($result)
        $result->query("SET NAMES utf8mb4");

    return $result;
}
function db_connect_hk()
{
    $result = new mysqli('pnet-pc-u1l2k1u6f5b9y6r8.mysql.polardb.rds.aliyuncs.com', 'xu_gm_2023', 'Xiaou$GM#DB$2023', 'hw_yzkofvs');

    if ($result)
        $result->query("SET NAMES utf8mb4");

    return $result;
}
function db_connect_as()
{
    $result = new mysqli('***************', 'root', 'Xiaou$GM#DB$2023', 'hw_yzkofvs');

    if ($result)
        $result->query("SET NAMES utf8mb4");

    return $result;
}
function db_connect_eu()
{
    $result = new mysqli('*************', 'root', 'Xiaou$GM#DB$2023', 'hw_yzkofvs');

    if ($result)
        $result->query("SET NAMES utf8mb4");

    return $result;
}
function db_connect_us()
{
    $result = new mysqli('*************', 'root', 'Xiaou$GM#DB$2023', 'hw_yzkofvs');

    if ($result)
        $result->query("SET NAMES utf8mb4");

    return $result;
}

function db_connect_me()
{
    $result = new mysqli('47.91.115.200', 'root', 'Xiaou$GM#DB$2023', 'hw_yzkofvs');

    if ($result)
        $result->query("SET NAMES utf8mb4");

    return $result;
}

function db_connect_ru()
{
    $result = new mysqli('45.89.188.213', 'root', 'Xiaou$GM#DB$2023', 'hw_yzkofvs');

    if ($result)
        $result->query("SET NAMES utf8mb4");

    return $result;
}

function db_connect_br()
{
    $result = new mysqli('47.253.85.92', 'root', 'Xiaou$GM#DB$2023', 'hw_yzkofvs');

    if ($result)
        $result->query("SET NAMES utf8mb4");

    return $result;
}

function db_connect_cn()
{
    $result = new mysqli('rdsn11tv355y20iijt67o.mysql.rds.aliyuncs.com', 'xiaou_app', 'XiaouGame2015', 'xiaou_db');

    if ($result)
        $result->query("SET NAMES utf8mb4");

    return $result;
}

function db_connect_starfoce()
{
    $result = new mysqli('43.136.97.89', 'root', 'Xiaou$GM#DB$2023', 'xiaou_db');

    if ($result)
        $result->query("SET NAMES utf8mb4");

    return $result;
}

//output database error msg
function output_json_db_error($response)
{
    $response->msg = 'Server database error!';
    $response->ret = -1;

    header('Content-type: application/json');
    echo json_encode($response);
}

function output_json_db_error_with_msg($response, $msg)
{
    $response->msg = 'Server database error:' . $msg;
    $response->ret = -1;

    header('Content-type: application/json');
    echo json_encode($response);
}

//output token error msg
function output_json_token_error($response)
{
    $response->msg = 'Token error!';
    $response->ret = -1;

    header('Content-type: application/json');
    echo json_encode($response);
}

//output parameters error msg
function output_json_para_error($response)
{
    $response->msg = 'Parameter error!';
    $response->ret = -1;

    header('Content-type: application/json');
    echo json_encode($response);
}


//output parameters error msg
function output_json_para_error_msg($response, $msg)
{
    $response->msg = $msg;
    $response->ret = -1;
    $response->error_code = ERROR_PARA;

    header('Content-type: application/json');
    echo json_encode($response);
}

function output_json_api_error($response,  $ret, $msg)
{
    $response->msg = $msg;
    $response->ret = $ret;

    header('Content-type: application/json');
    echo json_encode($response);
}

function output_json_register_error2($response,  $ret,  $msg)
{
    $response->msg = $msg;
    $response->ret = $ret;
    $response->error_code = ERROR_REGISTER;

    header('Content-type: application/json');
    echo json_encode($response);
}

function is_chip_id_exist($aConn, $response, $product_id)
{
    /*
    $lResult = $aConn->query("SELECT * FROM  T_snkvs_chip_list WHERE chip_id='{$product_id }'");
    if(!$lResult)
    {
        output_json_db_error_with_msg($response,mysqli_error($aConn));
        return false;
    }else{
        $lNum = $lResult->num_rows;

        if($lNum < 1)
        {
            output_json_api_error($response, -2, 'Chip id error!');
            return false;
        }
        $lResult->free();
    }
    return true;
*/
    $product_id = trim($product_id);
    $lCMD = "SELECT macid FROM  t_hw_chip_list WHERE macid_enc= ? ";
    $lStmt = $aConn->prepare($lCMD); //mysql client --> mysql server(network time)???
    if (!$lStmt) {
        output_json_db_error_with_msg($response, mysqli_error($aConn));
        return false;
    } else {
        $lStmt->bind_param("s", $product_id);

        if (!$lStmt->execute()) { //mysql client --> mysql server(network time)???
            output_json_db_error_with_msg($response, mysqli_error($aConn));
            return false;
        } else {
            $lResult = $lStmt->get_result();

            $lNum = $lResult->num_rows;

            if ($lNum < 1) {
                output_json_api_error($response, -2, 'Chip id error!');
                return false;
            }
            $lResult->free();
        }

        $lStmt->close();
    }

    return true;
}

function get_new_login_token($aConn, $user_id, $channel)
{
    global $LOGIN_TOKEN_KEY;
    global $SERVER_REGION_NAME_ARRAY;
    global $SERVER_REGION_CUR_IDX;

    $lTokenTable = 't_hw_users_logintoken';

    $lNewLoginToken = '';

    //get login_token
    $lResult = $aConn->query("SELECT user_id,login_token FROM {$lTokenTable} WHERE user_id = {$user_id}");

    if ($lResult) {
        $lNum = $lResult->num_rows;

        $lNow = date("Y-m-d | H:i:s");
        $lSrcStr = $user_id . $lNow . $LOGIN_TOKEN_KEY;
        $lMD5Str = md5($lSrcStr);
        $lSHA1Str = sha1($lMD5Str);

        $lNewLoginToken = $lSHA1Str;

        if ($lNum == 0) {
            $aConn->query("INSERT INTO {$lTokenTable} (user_id, login_token,login_channel)  VALUES ({$user_id}, '{$lSHA1Str}',{$channel})");
        } else {
            $aConn->query("UPDATE {$lTokenTable} SET login_token='{$lSHA1Str}',login_channel={$channel} WHERE user_id={$user_id} ");
        }

        $lResult->free();
    }

    return $lNewLoginToken;
}

function is_login_token_valid($aConn, $response, $user_id, $login_token)
{
    global $SERVER_REGION_NAME_ARRAY;
    global $SERVER_REGION_CUR_IDX;

    $lTokenTable = 't_hw_users_logintoken';

    $lResult = $aConn->query("SELECT user_id,login_token FROM {$lTokenTable} WHERE user_id=" . $user_id . "  ");

    if ($lResult) {
        $result_num_token = $lResult->num_rows;

        if ($result_num_token > 0) {
            $row = $lResult->fetch_assoc();
            $r_login_token =  $row['login_token'];
            if (trim($login_token) != trim($r_login_token)) {
                output_json_api_error($response, -3, 'Login token error!');
                return false;
            }
        } else {
            output_json_api_error($response, -3, 'Login token error!');
            return false;
        }
    } else {
        output_json_db_error_with_msg($response, mysqli_error($aConn));
        return false;
    }

    return true;
}
