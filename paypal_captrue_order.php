<?php

include(dirname(__FILE__) . "/games_funs.php");
include(dirname(__FILE__) . '/vendor/autoload.php');
require_once './log.php';

//初始化日志
$logHandler = new CLogFileHandler(sys_get_temp_dir() . DIRECTORY_SEPARATOR . "hw_paypal_captrue_order_" . date('Y-m-d') . ".log");
$log = Log::Init($logHandler, 1);

$paymentId = $_GET['token']; // PayPal在重定向时返回
$payerId = $_GET['PayerID']; // PayPal在重定向时返回

Log::Debug("token: $paymentId, PayerID: $payerId");

if (isset($paymentId) && isset($payerId)) {
    $gConn = db_connect();

    if (!$gConn) {
        exit;
    }

    // get paypal token
    $paypalAccessToken = "";
    $gResult = $gConn->query("SELECT token_type,access_token FROM t_hw_paypal_access_token ORDER BY id DESC LIMIT 1");
    if (!$gResult) {
        exit;
    } else {
        $lNum = $gResult->num_rows;

        if ($lNum > 0) {
            $row = $gResult->fetch_assoc();

            $token_type = $row['token_type'];
            $access_token = $row['access_token'];
            $paypalAccessToken = "$token_type $access_token";
        }

        $gResult->free();
    }

    // 准备请求头
    $headers = array(
        'Content-Type' => 'application/json',
        'Authorization' => $paypalAccessToken, // 您从PayPal获取的Access Token
    );

    $maxRetries = 3; // 最大重试次数
    $attempt = 0; // 当前重试次数
    while ($attempt < $maxRetries) {
        try {
            $captureResponse = WpOrg\Requests\Requests::post($PAYPAL_REST_URL . "/v2/checkout/orders/$paymentId/capture", $headers, array(), array("timeout" => 30, "connect_timeout" => 30));
            $captureBody = json_decode($captureResponse->body, true);
            break;
        } catch (WpOrg\Requests\Exception $e) {
            // 捕获其他异常，可能需要根据实际情况处理或直接抛出
            Log::Debug("Capture request exception: " . $e);

            // 重发请求
            $attempt++;
            if ($attempt == $maxRetries) {
                Log::Debug("Capture request limit.");
                break;
            }
        }
    }
}

$gConn->close();

header("Location: " . $PAYPAL_DEFAULT_PAGE);
exit;
