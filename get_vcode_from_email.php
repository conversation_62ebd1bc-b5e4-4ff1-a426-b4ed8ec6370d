<?php

include(dirname(__FILE__) . "/games_funs.php");
include(dirname(__FILE__) . "/PasswordHash.php");
require_once './log.php';

use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON>Mailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

require 'PHPMailer/src/PHPMailer.php';
require 'PHPMailer/src/SMTP.php';

//初始化日志 windwos下路径为：C:\Users\<USER>\AppData\Local\Temp
$logHandler = new CLogFileHandler(sys_get_temp_dir() . DIRECTORY_SEPARATOR . "email_code_error" . '.log');
$log = Log::Init($logHandler, 1);

/*
$gCode34Char = array('2','3'
        ,'4','5','6','7'
        ,'8','9','A','B'
        ,'C','D','E','F'
        ,'G','H','I','J'
        ,'K','L','M','N'
        ,'O','P','Q','R'
        ,'S','T','U','V'
        ,'W','X','Y','Z');
*/

class GLResponse
{

    public $msg = 'ok';
    public $ret = 0;

    public $email_no = '';
    public $left_secs = 0;
};

$response = new GLResponse();

$email_addr = $_GET['email'];
$product_id = $_GET['device_id'];
$lang = $_GET['lang'];
$channel = $_GET['channel'];
$token = $_GET['token'];

if ($email_addr == NULL || $product_id == NULL || $lang == NULL || $channel == NULL || $token == NULL) {
    output_json_para_error($response);
    exit;
}

if (0 == strlen($email_addr) || 0 == strlen($token) || 0 == strlen($product_id)) {
    output_json_para_error($response);
    exit;
}

$channel = intval($channel);
$lang = intval($lang);
$email_addr = trim($email_addr);
$product_id = trim($product_id);
$token = trim($token);

if (sha1($email_addr . $product_id . $lang . $channel . $VS_KEY) != $token) {
    output_json_token_error($response);
    exit;
}

if (!isValidEmailAddr($email_addr)) {
    output_json_api_error($response, -3, 'It isn`t a valid Email!');
    exit;
}

//connect database and query
$gConn = db_connect();

if (!$gConn) {
    output_json_db_error($response);
    exit;
}

$email_addr = mysqli_real_escape_string($gConn, $email_addr);
$product_id = mysqli_real_escape_string($gConn, $product_id);

if (!is_chip_id_exist($gConn, $response, $product_id)) {
    exit;
}

$lAES = new AESSSL($EMAILAES128KEY);
$lEncEmailAddr = $lAES->encrypt($email_addr);

$CODEERRORLIMITSECS = 15 * 60;

$result = $gConn->query("SELECT id,try_num,TIMESTAMPDIFF(SECOND, time, now()) AS secs FROM t_hw_email_verify_no WHERE email = '" . $lEncEmailAddr . "' ORDER BY id DESC LIMIT 1");
if (!$result) {
    output_json_db_error($response);
    exit;
} else {
    $result_num = $result->num_rows;
    if (1 == $result_num) {
        $row = $result->fetch_assoc();
        $lSecs = intval($row['secs']);
        $lTryNum = intval($row['try_num']);
        if ($lSecs < 60) {
            $response->left_secs = 60 - $lSecs;
            output_json_api_error($response, -4, 'repeat send email less 60 seconds');
            exit;
        }
        if ($lTryNum >= $EMAILCODEERRORCNT) {
            if ($lSecs < $CODEERRORLIMITSECS) {
                $response->left_secs = intval($CODEERRORLIMITSECS - $lSecs);
                output_json_api_error($response, -5, 'send email code limit [less 15*60 seconds]');
                exit;
            }
        }
    }
}

//$email_code = $gCode34Char[rand(0, 999999)%34].$gCode34Char[rand(0, 999999)%34].$gCode34Char[rand(0, 999999)%34].$gCode34Char[rand(0, 999999)%34];
$email_code = random_int(100000, 999999);

$mail = new PHPMailer(); //建立邮件发送类

$mail->IsSMTP(); // 使用SMTP方式发送
$mail->CharSet = 'UTF-8'; // 设置邮件的字符编码
$mail->Host = "smtp.sg.aliyun.com"; // 您的企业邮箱域名
$mail->SMTPAuth = true; // 启用SMTP验证功能
$mail->SMTPSecure = "ssl";
$mail->Port = "465"; //SMTP端口
$mail->Username = "<EMAIL>"; // 邮箱用户名(请填写完整的email地址)
$mail->Password = "wR4&vG0:qM2-hM6}"; // 授权码
$mail->From = "<EMAIL>"; //邮件发送者email地址
$mail->FromName = "gmarcade";
$mail->AddAddress("$email_addr", ""); //收件人地址，格式是AddAddress("收件人email","收件人姓名")
$mail->AddReplyTo("", "");
#$mail->AddAttachment("/var/tmp/file.tar.gz"); // 添加附件
$mail->IsHTML(false); // set email format to HTML //是否使用HTML格式
$mail->Subject = "Verification Code"; //邮件标题
$mail->Body = 'Your verification code is ' . $email_code . ' . This will be valid for 15 minutes. Please don`t share this with anyone .'; //邮件内容
// $mail->AltBody = "This is the body in plain text for non-HTML mail clients"; //附加信息，可以省略
if (!$mail->Send()) {
    output_json_api_error($response, -6, $mail->ErrorInfo);
    exit;
}

$cc = strval(random_int(100000, 999999));
$email_no = 'eno' . date("YmdHis") . $cc;


$result = $gConn->query("INSERT INTO  t_hw_email_verify_no (email, email_no, code, state, try_num, channel) VALUES ('" . $lEncEmailAddr . "', '" . $email_no . "', '" . $email_code . "', 0, 0, {$channel}  )");
if (!$result) {
    output_json_db_error($response);
    exit;
}

$response->email_no = $email_no;

header('Content-type: application/json');
echo json_encode($response);

$gConn->close();
