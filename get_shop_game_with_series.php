<?php

include(dirname(__FILE__) . "/games_funs.php");

class GLResponse
{
    public $msg = 'ok';
    public $ret = 0;

    public $gameList = NULL;
};

class GameDataSimple
{
    public $game_id = 0;
    public $game_main_icon = '';
    public $game_name = '';
    public $net_type = 0;
    public $origin_price = 0;
    public $price = 0;
    public $added = 0;
}

$response = new GLResponse();
//-----------------------------------------------------------

$user_id = $_GET['user_id'];
$login_token = $_GET['login_token'];
$product_id = $_GET['device_id'];
$lang = $_GET['lang'];
$channel = $_GET['channel'];
$series_id = $_GET['series_id'];
$start = $_GET['start'];
$token = $_GET['token'];

if ($user_id == NULL || $login_token == NULL || $product_id == NULL || $lang == NULL || $channel == NULL || $series_id == NULL || $start == NULL || $token == NULL) {
    output_json_para_error($response);
    exit;
}

$user_id = intval($user_id);
$lang = intval($lang);
$channel = intval($channel);
$series_id = intval($series_id);
$start = intval($start);
$product_id = trim($product_id);
$login_token = trim($login_token);
$token = trim($token);

if (sha1($user_id . $product_id . $lang . $channel . $series_id . $start . $VS_KEY) != $token) {
    output_json_token_error($response);
    exit;
}

$language_id = $gDLangTSuffix;
if (isset($gAllLanguage[$lang])) {
    $language_id = $lang;
}

$limit = 20; // 每页20条记录
$offset = ($start - 1) * $limit;

//-----------------------------------------------------------

$gConn = db_connect();

if (!$gConn) {
    output_json_db_error($response);
    exit;
}

$product_id = mysqli_real_escape_string($gConn, $product_id);

if (!is_login_token_valid($gConn, $response, $user_id, $login_token)) {
    exit;
}

$GAME_CONDI = '';

$game_ids = '';
$gResult = $gConn->query("SELECT include_game FROM t_hw_all_channel WHERE channel={$channel}");
if (!$gResult) {
    output_json_db_error($response);
    exit;
} else {
    $lNum = $gResult->num_rows;

    if ($lNum == 1) {
        $row = $gResult->fetch_assoc();

        $game_ids = trim($row['include_game']);
    } else {
        output_json_api_error($response, -4, 'channel error!');
        exit;
    }

    $gResult->free();
}


if ($game_ids == '-1') {
    $GAME_CONDI = '';
} else {
    if (strlen($game_ids) == 0) {
        $result = $gConn->query("SELECT include_game FROM t_hw_all_server_region WHERE region_id={$SERVER_REGION_CUR_IDX}");
        if (!$result) {
            output_json_db_error($response);
            exit;
        } else {

            $result_num = $result->num_rows;

            if ($result_num > 0) {
                $row = $result->fetch_assoc();

                $game_ids = trim($row['include_game']);
            }
            $result->free();
        }
    }
    $GAME_CONDI = "game_id IN ($game_ids) AND";
}

$lMyGamesArray = [];

$gResult = $gConn->query("SELECT games FROM t_yzvs_users_games WHERE user_id = {$user_id}");
if (!$gResult) {
    output_json_db_error_with_msg($response, mysqli_error($gConn));
    exit();
} else {

    $lNum = $gResult->num_rows;

    if ($lNum > 0) {
        $lRow = $gResult->fetch_assoc();
        $lGames = trim($lRow['games']);
        if (strlen($lGames) > 0) {
            $lMyGamesArray = array_flip(explode(',', $lGames));
        }
    }

    $gResult->free();
}

$lMyGamesCnt = COUNT($lMyGamesArray);
$gResult = $gConn->query("SELECT ta.game_id,ta.main_name,ta.net_type,ta.game_main_icon,ta.origin_price,ta.price,tb.main_name_t FROM t_yzvs_all_games_common AS ta INNER JOIN t_yzvs_all_games_common_lang_{$language_id} AS tb ON ta.game_id=tb.game_id WHERE ta.series_id={$series_id} AND ta.game_id IN ($game_ids) AND ta.is_show=1 AND ta.is_shop=1 ORDER BY ta.game_id DESC LIMIT {$limit} OFFSET {$offset}");
if (!$gResult) {
    output_json_db_error_with_msg($response, mysqli_error($gConn));
    exit();
} else {
    $lNum = $gResult->num_rows;

    for ($i = 0; $i < $lNum; $i++) {
        $lRow = $gResult->fetch_assoc();

        $lGame = new GameDataSimple();
        $lGame->game_id = intval($lRow['game_id']);
        if ($lRow['main_name_t'] != NULL) {
            $lGame->game_name = trim($lRow['main_name_t']);
        } else {
            $lGame->game_name = trim($lRow['main_name']);
        }
        $lGame->net_type = intval($lRow['net_type']);
        if ($lRow['game_main_icon'] != NULL) {
            $lGame->game_main_icon = $SERVER_RES_PREFIX[$SERVER_REGION_CUR_IDX] . $YZVS_GAME_ICON_PREFIX . trim($lRow['game_main_icon']);
        }
        $lGame->origin_price = intval($lRow['origin_price']);
        $lGame->price = intval($lRow['price']);
        if ($lMyGamesCnt > 0 && isset($lMyGamesArray[$lGame->game_id])) {
            $lGame->added = 1;
        }

        $response->gameList[] = $lGame;
    }

    $gResult->free();
}


header('Content-type: application/json');
echo json_encode($response);

$gConn->close();
