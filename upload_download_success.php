<?php

include(dirname(__FILE__) . "/games_funs.php");

class GLResponse
{
    public $msg = 'ok';
    public $ret = 0;
};

$response = new GLResponse();
//-----------------------------------------------------------

$user_id = $_POST['user_id'];
$login_token = $_POST['login_token'];
$product_id = $_POST['device_id'];
$lang = $_POST['lang'];
$channel = $_POST['channel'];
$game_id = $_POST['game_id'];
$token = $_POST['token'];

if ($user_id == NULL || $login_token == NULL || $product_id == NULL || $lang == NULL || $channel == NULL || $game_id == NULL || $token == NULL) {
    output_json_para_error($response);
    exit;
}

$user_id = intval($user_id);
$lang = intval($lang);
$channel = intval($channel);
$game_id = intval($game_id);
$product_id = trim($product_id);
$login_token = trim($login_token);
$token = trim($token);

if (sha1($user_id . $product_id . $lang . $channel . $game_id . $VS_KEY) != $token) {
    output_json_token_error($response);
    exit;
}

$language_id = $gDLangTSuffix;
if (isset($gAllLanguage[$lang])) {
    $language_id = $lang;
}

//-----------------------------------------------------------

$gConn = db_connect();

if (!$gConn) {
    output_json_db_error($response);
    exit;
}

$product_id = mysqli_real_escape_string($gConn, $product_id);

if (!is_login_token_valid($gConn, $response, $user_id, $login_token)) {
    exit;
}

$real_chip = '';
$gResult = $gConn->query("SELECT macid FROM t_hw_chip_list WHERE macid_enc='{$product_id}'");
if (!$gResult) {
    output_json_db_error($response);
    exit;
} else {
    $result_num = $gResult->num_rows;

    if ($result_num != 1) {
        output_json_api_error($response, -2, 'Chip id error!');
        exit;
    }

    $row = $gResult->fetch_assoc();
    $real_chip = $row['macid'];

    $gResult->free();
}

$gResult = $gConn->query("SELECT channel FROM t_hw_all_channel WHERE channel={$channel}");
if (!$gResult) {
    output_json_db_error($response);
    exit;
} else {
    $lNum = $gResult->num_rows;

    if ($lNum != 1) {
        output_json_api_error($response, -4, 'channel error!');
        exit;
    }

    $gResult->free();
}

$gConn->begin_transaction();

$gResult = $gConn->query("INSERT INTO t_hw_download_game_record (chip_id, user_id, game_id, channel) VALUES ('{$real_chip}', {$user_id}, {$game_id}, {$channel})");
if (!$gResult) {
    output_json_db_error_with_msg($response, mysqli_error($gConn));
    $gConn->rollback();
    exit();
}

$gResult = $gConn->query("UPDATE t_yzvs_all_games_common SET downloads=downloads+1 WHERE game_id={$game_id}");
if (!$gResult) {
    output_json_db_error_with_msg($response, mysqli_error($gConn));
    $gConn->rollback();
    exit();
}

$gConn->commit();


header('Content-type: application/json');
echo json_encode($response);

$gConn->close();