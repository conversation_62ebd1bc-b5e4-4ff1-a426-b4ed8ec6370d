<?php

include(dirname(__FILE__) . "/games_funs.php");

class GLResponse
{
    public $msg = 'ok';
    public $ret = 0;

    public $game_type_list = NULL;
};

class GameTypeItem
{

    public $game_type = 0;
    public $game_list = NULL;
}

class GameItem
{
    public $game_id = 0;
    public $game_main_icon = '';
    public $game_name = '';
    public $game_desc = '';
    public $game_url = '';
    public $rom_pkg_name = '';
    public $game_suffix = '';
    public $game_crc = '';
    public $game_size = 0;

    public $game_vs_type = 0;
    public $has_records = 0;
}

class GameDescItem
{
    public $game_name = '';
    public $game_desc = '';
}

$response = new GLResponse();
//-----------------------------------------------------------

$user_id = $_GET['user_id'];
$login_token = $_GET['login_token'];
$product_id = $_GET['device_id'];
$lang = $_GET['lang'];
$channel = $_GET['channel'];
$token = $_GET['token'];

if ($user_id == NULL || $login_token == NULL || $product_id == NULL || $lang == NULL || $channel == NULL || $token == NULL) {
    output_json_para_error($response);
    exit;
}

$user_id = intval($user_id);
$lang = intval($lang);
$channel = intval($channel);
$product_id = trim($product_id);
$login_token = trim($login_token);
$token = trim($token);

if (sha1($user_id . $product_id . $lang . $channel . $VS_KEY) != $token) {
    output_json_token_error($response);
    exit;
}

$language_id = $gDLangTSuffix;
if (isset($gAllLanguage[$lang])) {
    $language_id = $lang;
}


//-----------------------------------------------------------

$gConn = db_connect();

if (!$gConn) {
    output_json_db_error($response);
    exit;
}

$product_id = mysqli_real_escape_string($gConn, $product_id);

if (!is_chip_id_exist($gConn, $response, $product_id)) {
    exit;
}

if (!is_login_token_valid($gConn, $response, $user_id, $login_token)) {
    exit;
}

$GAME_CONDI = '';

$game_ids = '';
$gResult = $gConn->query("SELECT include_game FROM t_hw_all_channel WHERE channel={$channel}");
if (!$gResult) {
    output_json_db_error($response);
    exit;
} else {
    $lNum = $gResult->num_rows;

    if ($lNum == 1) {
        $row = $gResult->fetch_assoc();

        $game_ids = trim($row['include_game']);
    } else {
        output_json_api_error($response, -4, 'channel error!');
        exit;
    }

    $gResult->free();
}


if ($game_ids == '-1') {
    $GAME_CONDI = '';
} else {
    if (strlen($game_ids) == 0) {
        $result = $gConn->query("SELECT include_game FROM t_hw_all_server_region WHERE region_id={$SERVER_REGION_CUR_IDX}");
        if (!$result) {
            output_json_db_error($response);
            exit;
        } else {

            $result_num = $result->num_rows;

            if ($result_num > 0) {
                $row = $result->fetch_assoc();

                $game_ids = trim($row['include_game']);
            }
            $result->free();
        }
    }
    $GAME_CONDI = "game_id IN ($game_ids) AND";
}



$game_type_list = [];

$all_gameIds = '';

$lGameTable = "t_hw_all_games_common_lang_" . $language_id;
$lGameSQL = "SELECT * FROM t_hw_all_games_common WHERE $GAME_CONDI ta.is_show=1 ORDER BY ta.series_id";

$result = $gConn->query("SHOW TABLES LIKE '{$lGameTable}'");
if (!$result) {
    output_json_db_error($response);
    exit;
} else {
    $result_num = $result->num_rows;

    if ($result_num > 0) {
        if (strlen($GAME_CONDI) > 0) {
            $GAME_CONDI = "ta.game_id IN ($game_ids) AND";
        }
        $lGameSQL = "SELECT * FROM t_hw_all_games_common AS ta INNER JOIN t_hw_all_games_common_lang_{$language_id} AS tb ON ta.game_id=tb.game_id WHERE $GAME_CONDI ta.is_show=1 ORDER BY ta.series_id";
    } else {
        $lGameTable = "";
    }
}

$result = $gConn->query($lGameSQL);
if (!$result) {
    output_json_db_error($response);
    exit;
} else {
    $result_num = $result->num_rows;

    for ($i = 0; $i < $result_num; $i++) {
        $row = $result->fetch_assoc();

        $game_type = intval($row['series_id']);
        $game_id = intval($row['game_id']);

        $lGameItem = new GameItem();

        $lGameItem->game_id = $game_id;
        $all_gameIds = $all_gameIds . "{$game_id},";
        if (strlen(trim($row['game_main_icon'])) > 0) {
            $lGameItem->game_main_icon = $SERVER_RES_PREFIX[$SERVER_REGION_CUR_IDX] . trim($row['game_main_icon']);
        }
        if (strlen(trim($row['game_url'])) > 0) {
            $lGameItem->game_url = $SERVER_RES_PREFIX[$SERVER_REGION_CUR_IDX] . trim($row['game_url']);
        }
        $lGameItem->rom_pkg_name = trim($row['rom_pkg_name']);
        $lGameItem->game_suffix = trim($row['game_suffix']);
        $lGameItem->game_crc = trim($row['game_crc']);
        $lGameItem->game_size = intval($row['game_size']);
        if (isset($row['game_name'])) {
            $lGameItem->game_name = trim($row['game_name']);
        } else {
            $lGameItem->game_name = trim($row['game_name_common']);
        }
        $lGameItem->game_desc = trim($row['game_text']);
        $lGameItem->game_vs_type = intval($row['content_type']);
        $lGameItem->has_records = intval($row['has_records']);

        if (!isset($game_type_list[$game_type])) {
            $lGameTypeItem = new GameTypeItem();
            $lGameTypeItem->game_type = $game_type;
            $game_type_list[$game_type] = $lGameTypeItem;
        }
        $game_type_list[$game_type]->game_list[] = $lGameItem;
    }

    $result->free();
}

if (strlen($all_gameIds) > 0) {
    $all_gameIds = trim($all_gameIds, ",");

    $game_info_arr = [];

    $result = $gConn->query("SELECT game_id,game_name,game_desc FROM t_hw_all_games_extra_info WHERE lang={$language_id} AND game_id IN ($all_gameIds)");
    if (!$result) {
        output_json_db_error($response);
        exit;
    } else {
        $result_num = $result->num_rows;

        for ($i = 0; $i < $result_num; $i++) {
            $row = $result->fetch_assoc();

            $game_id = intval($row['game_id']);

            $lGameDescItem = new GameDescItem();
            $lGameDescItem->game_name = trim($row['game_name']);
            $lGameDescItem->game_desc = trim($row['game_desc']);

            $game_info_arr[$game_id] = $lGameDescItem;
        }

        $result->free();
    }
}

foreach ($game_type_list as $key => $value) {
    if ($value->game_list != NULL) {
        foreach ($value->game_list as $key2 => $value2) {
            if (isset($game_info_arr[$value2->game_id])) {
                $value2->game_name = $game_info_arr[$value2->game_id]->game_name;
                $value2->game_desc = $game_info_arr[$value2->game_id]->game_desc;
            }
        }
    }
    $response->game_type_list[] = $value;
}

//-----------------------------------------------------------
header('Content-type: application/json');
echo json_encode($response);

$gConn->close();
