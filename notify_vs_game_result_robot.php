<?php

include (dirname(__FILE__)."/games_funs.php");

include (dirname(__FILE__)."/player_common_struct.php");

//---------------- JSON DATA CLASS ---------------------------

class GLResponse{

    public $msg = 'ok';
    public $ret = 0;

    public $playerGameInfoList = NULL;
};

class PlayerGameInfo{
    public $playerInfo = NULL;
    public $vsGameInfo = NULL;
}

class VSGameInfo{

    public $level = 1;
    public $rankLevel = 1;
    public $levelExp = 0;
    public $levelTotalExp = 500;

    public $win = 0;
    public $lose = 0;
    public $draw = 0;
    public $escape = 0;

    //Extra vars
    public $totalExp = 0;
}

$response = new GLResponse();

//args
$user_id = $_POST['user_id'];
// $login_token = $_POST['login_token'];
// $product_id = $_POST['device_id'];

$game_id = $_POST['game_id'];
$p1_id = $_POST['p1_id'];
$p2_id = $_POST['p2_id'];
$p1_ip = $_POST['p1_ip'];
$p2_ip = $_POST['p2_ip'];
$result = $_POST['result'];

$token = $_POST['token'];

/*
$user_id = 1000;
$login_token = 'e33ae3984260e1b1a8dfbf8af86f509c0a3b162e';
$product_id = '';

$game_id = 0;
$p1_id = 1000;
$p2_id = 1001;
$p1_ip = '***********';
$p2_ip = '***********';
$result = 2;

$token = 'aaaaa';
 */

if(NULL === $user_id 
        // || NULL === $login_token 
        // || NULL === $product_id
        || NULL === $game_id 
        || NULL === $p1_id 
        || NULL === $p2_id 
        || NULL === $p1_ip 
        || NULL === $p2_ip 
        || NULL === $result
        || NULL === $token
  ){
    output_json_para_error($response);
    exit;
}else{
    $user_id = intval($user_id);
    // $login_token = trim($login_token);
    // $product_id = trim($product_id);
    $token = trim($token);

    $game_id = intval($game_id);
    $p1_id = intval($p1_id);
    $p2_id = intval($p2_id);
    $p1_ip = ip2long($p1_ip);
    $p2_ip = ip2long($p2_ip);
    $result = intval($result);

    if($user_id <= 0
            || 0 == strlen($token)
            // || $LOGINTOKENLEN != strlen($login_token)
            || $game_id < 0
            || $p1_id < 0
            || $p2_id < 0
            || ($p1_id != $user_id && $p2_id != $user_id)
            || $result < 0 || $result > 2 //0,1,2
        ){
            output_json_para_error($response);
            exit;
        }
}

if(sha1($user_id.$game_id.$p1_id.$p2_id.$result.$VS_KEY) != $token)
{
    output_json_token_error($response);
    exit;
}

//-----------------------------------------------------------

$gConn = db_connect();

if(!$gConn)
{
    output_json_db_error($response);
    exit;
}

// $product_id = mysqli_real_escape_string($gConn, $product_id);
// if (!is_chip_id_exist($gConn, $response, $product_id)) {
//     exit;
// }

// if(!is_login_token_valid($gConn,$response,$user_id,$login_token)){ exit; }

//------------------------------------------------------------------------------------

$vs_result = 0;
switch($result){
case 0:
    $vs_result = DB_P1_WIN;//P1 WIN
    break;
case 1:
    $vs_result = DB_P2_WIN;//P2 WIN
    break;
case 2:
    $vs_result = DB_PP_DRAW;//DRAW
    break;
}

$gConn->begin_transaction();

// $gResult = $gConn->query("SELECT id,match_time FROM t_hw_user_match_game_{$game_id} WHERE p1_id={$p1_id} AND p2_id={$p2_id} ORDER BY `id` DESC LIMIT 1 FOR UPDATE");
// if(!$gResult){
//     output_json_db_error_with_msg($response,mysqli_error($gConn));
//     exit;
// }else{
//     $lNum = $gResult->num_rows;

//     if(1 == $lNum){
//         $lRow = $gResult->fetch_assoc();
//         $lMatchTime = trim($lRow['match_time']);
//         $lDiffSecs = abs(strtotime("now") - strtotime($lMatchTime));
//         if($lDiffSecs < 15){
//             output_json_api_error($response, -4, 'Match time abnormal!');
//             exit;
//         }
//     }
//     $gResult->free();
// }

$gResult = $gConn->query("INSERT INTO  t_hw_user_match_game_{$game_id} (p1_id,p2_id,p1_ip,p2_ip,vs_result) VALUES({$p1_id},{$p2_id},{$p1_ip},{$p2_ip},{$vs_result} )" );
if (!$gResult) 
{
    output_json_db_error($response);
    $gConn->rollback();
    exit;
}

$gConn->commit();

//get P1/P2 game info
$lPGameInfo = array(NULL,NULL);
$gResult = $gConn->query("SELECT * FROM t_hw_user_scores_game_{$game_id} WHERE user_id={$p1_id} OR user_id={$p2_id}");
if(!$gResult){
    output_json_db_error_with_msg($response,mysqli_error($gConn));
    exit;
}else{
    $lNum = $gResult->num_rows;

    for($i=0;$i<$lNum;$i++){
        $lRow = $gResult->fetch_assoc();
        $lCurUserID = intval($lRow['user_id']);

        $lItem = new VSGameInfo();

        $lItem->win = intval($lRow['win']);
        $lItem->lose = intval($lRow['lose']);
        $lItem->draw = intval($lRow['draw']);
        $lItem->escape = intval($lRow['escape']);

        $lItem->level = intval($lRow['level']);
        
        
        $lItem->userID = $lCurUserID;
        $lItem->totalExp = intval($lRow['points']);

        if($p1_id == $lCurUserID){
            $lPGameInfo[0] = $lItem;
        }else if($p2_id == $lCurUserID){
            $lPGameInfo[1] = $lItem;
        }else{
            continue;
        }
    }
    for($i=0;$i<2;$i++){
        if(NULL === $lPGameInfo[$i]){
            $lPGameInfo[$i] = new VSGameInfo();
            $lPID = (0 == $i ? $p1_id : $p2_id);
            $lResult = $gConn->query("INSERT INTO t_hw_user_scores_game_{$game_id} (user_id) VALUES({$lPID})");
            if(!$lResult){
                output_json_db_error_with_msg($response,mysqli_error($gConn));
                exit;
            }
        }
    }
    $gResult->free();
}

/*---------------------------------------------------*/

//>>>>>> P1
$user[$p1_id] = new PlayerGameInfo();
$user[$p1_id]->vsGameInfo = NULL;
//>>>>>> P2
$user[$p2_id] = new PlayerGameInfo();
$user[$p2_id]->vsGameInfo = NULL;
//

$lCurUserIDArray = array($p1_id,$p2_id);
$lPlayerInfoArray = getPlayerInfo($gConn,$lCurUserIDArray,TYPE_PINFO_SUB,true,EXTRA_PINFO_NULL);
if(NULL == $lPlayerInfoArray){
    //need process ???
}else{
    if(isset($lPlayerInfoArray[$p1_id]))
        $user[$p1_id]->playerInfo = $lPlayerInfoArray[$p1_id]; 
    if(isset($lPlayerInfoArray[$p2_id]))
        $user[$p2_id]->playerInfo = $lPlayerInfoArray[$p2_id]; 
}

//------------------------------------------------------------------------------------------------------------------------------

if(DB_PP_DRAW == $vs_result){//DRAW
    $lPGameInfo[0]->draw += 1;
    $lPGameInfo[1]->draw += 1;

    $lResult =  $gConn->query("UPDATE t_hw_user_scores_game_{$game_id} SET draw = CASE user_id WHEN {$p1_id} THEN draw+1 WHEN {$p2_id} THEN draw+1 END WHERE user_id IN({$p1_id},{$p2_id})");
    if(!$lResult){
        //is need process ???
    }else{
        //is need process ???
    }
}else{
    $w_level = 0;
    $l_level = 0;
    $lWinPoints = 0;
    $lLosePoints = 0;

    if(DB_P1_WIN == $vs_result){//P1 WIN
        $w_level = $lPGameInfo[0]->level;
        $l_level = $lPGameInfo[1]->level;
    }else{//P2 WIN
        $w_level = $lPGameInfo[1]->level;
        $l_level = $lPGameInfo[0]->level;
    }
    //----------------------------------------------------------
    //get new winner points and level
    if($w_level <= 4)   // L1 - L4
    {
        if($l_level == $w_level)
        {
            $lWinPoints = 100;
            $lLosePoints = 50;
        }
        else if($l_level<$w_level)
        {
            $lWinPoints = 100 - ($w_level - $l_level)*10;
            $lLosePoints = 50 - (($w_level - $l_level)*5);
        }	
        else 
        {
            if(($l_level - $w_level) > 6)
            {
                $lWinPoints = 100;

                if($l_level>=5 && $l_level<=8)
                {
                    $lLosePoints = 55;
                }
                else if($l_level>=9 && $l_level<=12)
                {
                    $lLosePoints = 60;
                }
                else if($l_level>=13 && $l_level<=16)
                {
                    $lLosePoints = 65;
                }
                else
                {
                    $lLosePoints = 70;
                }
            }
            else
            {
                $lWinPoints = 100 + ($l_level - $w_level)*10;
                if($l_level>=1 && $l_level<=4)
                {
                    $lLosePoints = 50 + (($l_level - $w_level)*5);
                }
                else if($l_level>=5 && $l_level<=8)
                {
                    $lLosePoints = 55 + (($l_level - $w_level)*5);
                }
                else if($l_level>=9 && $l_level<=12)
                {
                    $lLosePoints = 60 + (($l_level - $w_level)*5);
                }
                else
                {
                }
            }
        }	
    }
    else if($w_level <= 8)  // L5 - L8
    {
        //echo 'lv5 - lv8\n';

        if($l_level == $w_level)
        {
            $lWinPoints = 95;
            $lLosePoints = 55;

        }
        else if($l_level<$w_level)
        {
            if(($w_level - $l_level) > 6)
            {
                $lWinPoints = 1;
                $lLosePoints = 0;
            }
            else
            {
                $lWinPoints = 95 - ($w_level - $l_level)*10;

                if($l_level <= 4)
                {
                    $lLosePoints = 50 - (($w_level - $l_level)*5);
                }
                else
                {
                    $lLosePoints = 55 - (($w_level - $l_level)*5);
                }
            }
        }	
        else 
        {
            if(($l_level - $w_level) > 6)
            {
                $lWinPoints = 95;

                if($l_level>=9 && $l_level<=12)
                {
                    $lLosePoints = 60;
                }
                else if($l_level>=13 && $l_level<=16)
                {
                    $lLosePoints = 65;
                }
                else
                {
                    $lLosePoints = 70;
                }
            }
            else
            {
                $lWinPoints = 95 + ($l_level - $w_level)*10;
                if($l_level>=5 && $l_level<=8)
                {
                    $lLosePoints = 55 + (($l_level - $w_level)*5);
                }
                else if($l_level>=9 && $l_level<=12)
                {
                    $lLosePoints = 60 + (($l_level - $w_level)*5);
                }
                else if($l_level>=13 && $l_level<=16)
                {
                    $lLosePoints = 65 + (($l_level - $w_level)*5);
                }
                else
                {
                }
            }
        }	
    }
    else if($w_level <= 12) // L9 - L12
    {
        if($l_level == $w_level)
        {
            $lWinPoints = 90;
            $lLosePoints = 60;
        }
        else if($l_level<$w_level)
        {
            if(($w_level - $l_level) > 6)
            {
                $lWinPoints = 1;
                $lLosePoints = 0;
            }
            else
            {
                $lWinPoints = 90 - ($w_level - $l_level)*10;
                if($l_level <= 4)
                {
                    $lLosePoints = 50 - (($w_level - $l_level)*5);
                }
                else if($l_level>=5 && $l_level<=8)
                {
                    $lLosePoints = 55 - (($w_level - $l_level)*5);
                }
                else 
                {
                    $lLosePoints = 60 - (($w_level - $l_level)*5);
                }
            }
        }	
        else 
        {
            if(($l_level - $w_level) > 6)
            {
                $lWinPoints = 90;
                if($l_level>=13 && $l_level<=16)
                {
                    $lLosePoints = 65;
                }
                else
                {
                    $lLosePoints = 70;
                }
            }
            else
            {
                $lWinPoints = 90 + ($l_level - $w_level)*10;
                if($l_level>=9 && $l_level<=12)
                {
                    $lLosePoints = 60 + (($l_level - $w_level)*5);
                }
                else if($l_level>=13 && $l_level<=16)
                {
                    $lLosePoints = 65 + (($l_level - $w_level)*5);
                }
                else
                {
                    $lLosePoints = 70 + (($l_level - $w_level)*5);
                }
            }
        }	
    }
    else if($w_level <= 16) // L13 - L16	
    {
        if($l_level == $w_level)
        {
            $lWinPoints = 85;
            $lLosePoints = 65;
        }
        else if($l_level<$w_level)
        {
            if(($w_level - $l_level) > 6)
            {
                $lWinPoints = 1;
                $lLosePoints = 0;
            }
            else
            {
                $lWinPoints = 85 - ($w_level - $l_level)*10;
                if($l_level>=5 && $l_level<=8)
                {
                    $lLosePoints = 55 - (($w_level - $l_level)*5);
                }
                if($l_level>=9 && $l_level<=12)
                {
                    $lLosePoints = 60 - (($w_level - $l_level)*5);
                }
                else
                {
                    $lLosePoints = 65 - (($w_level - $l_level)*5);
                }
            }
        }	
        else 
        {
            if(($l_level - $w_level) > 6)
            {
                $lWinPoints = 85;
                $lLosePoints = 70;
            }
            else
            {
                $lWinPoints = 85 + ($l_level - $w_level)*10;
                if($l_level>=13 && $l_level<=16)
                {
                    $lLosePoints = 65 + (($l_level - $w_level)*5);
                }
                else
                {
                    $lLosePoints = 70 + (($l_level - $w_level)*5);
                }
            }
        }	
    }
    else
    {
        if($l_level == $w_level)
        {
            $lWinPoints = 80;
            $lLosePoints = 70;
        }
        else if($l_level<$w_level)
        {
            if(($w_level - $l_level) > 6)
            {
                $lWinPoints = 1;
                $lLosePoints = 0;
            }
            else
            {
                $lWinPoints = 80 - ($w_level - $l_level)*10;
                if($l_level>=9 && $l_level<=12)
                {
                    $lLosePoints = 60 - (($w_level - $l_level)*5);
                }
                if($l_level>=13 && $l_level<=16)
                {
                    $lLosePoints = 65 - (($w_level - $l_level)*5);
                }
                else
                {
                    $lLosePoints = 70 - (($w_level - $l_level)*5);
                }
            }
        }	
        else 
        {
            if(($l_level - $w_level) > 6)
            {
                $lWinPoints = 80;
                $lLosePoints = 70;
            }
            else
            {
                $lWinPoints = 80 + ($l_level - $w_level)*10;
                $lLosePoints = 70 + (($l_level - $w_level)*5);
            }
        }	
    }
    //40级不再增加经验
    if($w_level >= gMaxGameLevel) $lWinPoints = 0;
    
    //----------------------------------------------------------
    
    $lRealWinPoints = $lWinPoints;
    $lRealLosePoints = $lLosePoints;

    $lWinnerID = 0; $lWinnerIndex = 0;
    $lLoserID = 0;$lLoserIndex = 0;
    if(DB_P1_WIN == $vs_result){//P1 WIN
        $lWinnerID = $p1_id; $lWinnerIndex = 0;
        $lLoserID = $p2_id; $lLoserIndex = 1;
    }else{//P2 WIN
        $lWinnerID = $p2_id; $lWinnerIndex = 1;
        $lLoserID = $p1_id; $lLoserIndex = 0;
    }

    $lPGameInfo[$lWinnerIndex]->totalExp += $lRealWinPoints;
    $lPGameInfo[$lLoserIndex]->totalExp -= $lRealLosePoints;
    if($lPGameInfo[$lLoserIndex]->totalExp < 0) $lPGameInfo[$lLoserIndex]->totalExp = 0;

    $lPGameInfo[$lWinnerIndex]->win += 1;
    $lPGameInfo[$lLoserIndex]->lose += 1;
    //---

    $lPGameInfo[$lWinnerIndex]->level = getGDLevelFromExp($lPGameInfo[$lWinnerIndex]->totalExp);
    $lPGameInfo[$lLoserIndex]->level = getGDLevelFromExp($lPGameInfo[$lLoserIndex]->totalExp);

    //------------------------------------------------------------
    $lResult =  $gConn->query("UPDATE t_hw_user_scores_game_{$game_id} SET points = CASE user_id WHEN {$p1_id} THEN {$lPGameInfo[0]->totalExp} WHEN {$p2_id} THEN {$lPGameInfo[1]->totalExp} END, level = CASE user_id WHEN {$p1_id} THEN {$lPGameInfo[0]->level} WHEN {$p2_id} THEN {$lPGameInfo[1]->level} END, win = CASE user_id WHEN {$p1_id} THEN {$lPGameInfo[0]->win} WHEN {$p2_id} THEN {$lPGameInfo[1]->win} END, lose = CASE user_id WHEN {$p1_id} THEN {$lPGameInfo[0]->lose} WHEN {$p2_id} THEN {$lPGameInfo[1]->lose} END WHERE user_id IN({$p1_id},{$p2_id})");
    if(!$lResult){
        //is need process ???
    }else{
        //is need process ???
    }
    //------------------------------------------------------------
}
//------------------------------------------------------------
for($i=0;$i<2;$i++){
    $lPGameInfo[$i]->rankLevel = getRankLevelFromLevel($lPGameInfo[$i]->level); 
    getGDLevelExpFromPL($lPGameInfo[$i]->totalExp,$lPGameInfo[$i]->level,$lPGameInfo[$i]->levelTotalExp,$lPGameInfo[$i]->levelExp); 
}

//>>>>>> P1
$user[$p1_id]->vsGameInfo = $lPGameInfo[0];
//>>>>>> P2
$user[$p2_id]->vsGameInfo = $lPGameInfo[1];

//------------------------------------------------------------------------------------------------------------------------------

$response->playerGameInfoList[0] = $user[$p1_id];
$response->playerGameInfoList[1] = $user[$p2_id];

header('Content-type: application/json');
echo json_encode($response);

$gConn->close();

?>
