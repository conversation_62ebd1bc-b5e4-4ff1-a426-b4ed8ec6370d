<?php

include(dirname(__FILE__) . "/games_funs.php");

class Response
{
    public $msg = 'ok';
    public $ret = 0;
    public $error_code = ERROR_NOERROR;

    public $app_url = '';        //enc file download url1
    public $app_urla = '';        //enc file download url2

    public $apk_url = '';        //apk download url

    public $app_version = '0';
    public $key_version = '0';
    public $version_name = '';
    public $size = 0;
    public $info = '';
    public $enc_file_crc = '';
    public $app_file_crc = '';

    public $system_msg = '';
};

$response = new Response();

$product_id = $_GET['device_id'];
$app_vcode = $_GET['app_vcode'];
$channel = $_GET['channel'];
$lang = $_GET['lang'];
$token = $_GET['token'];

if ($channel == '11000') {
    if (NULL == $token || NULL == $channel || NULL == $lang || NULL === $app_vcode) {
        output_json_para_error_msg($response, 'args is error!');
        exit;
    }
} else {
    if (NULL == $product_id || NULL == $token || NULL == $channel || NULL == $lang || NULL === $app_vcode) {
        output_json_para_error_msg($response, 'args is error!');
        exit;
    }
}


$product_id = trim($product_id);
$app_vcode = trim($app_vcode);
$channel = intval($channel);
$lang = intval($lang);
$token = trim($token);

if (sha1($product_id . $app_vcode . $lang . $channel . $VS_KEY) != $token) {
    output_json_token_error($response);
    exit;
}

//-----------------------------------------------------------

$app_vname = '';

//connect database and query
$gConn = db_connect();

if (!$gConn) {
    output_json_db_error($response);
    exit;
}

$product_id = mysqli_real_escape_string($gConn, $product_id);
$app_vcode = mysqli_real_escape_string($gConn, $app_vcode);

$lAES = new AESSSL($AES128_KEY);

$chipIsExist = true;
$lRegChannel = 0;
$real_macid = "test_mac";
if (!isset($NOT_AUTH_CHANNEL[$channel])) {
    $result = $gConn->query("SELECT macid,last_channel FROM  t_hw_chip_list WHERE macid_enc='{$product_id}'");

    if (!$result) {
        output_json_db_error($response);
        exit;
    } else {

        $result_num = $result->num_rows;

        if ($result_num != 1) {
            if ($channel == 11000) {
                $chipIsExist = false;
            } else {
                output_json_register_error2($response, -2, 'chip id error!');
                exit;
            }
        } else {
            $row = $result->fetch_assoc();

            if ($row) {
                $real_macid = $row['macid'];
                $lRegChannel = intval($row['last_channel']);
            }
        }

        $result->free();
    }
}


$gResult = $gConn->query("SELECT channel FROM t_hw_all_channel WHERE channel={$channel}");
if (!$gResult) {
    output_json_db_error($response);
    exit;
} else {
    $lNum = $gResult->num_rows;

    if ($lNum != 1) {
        output_json_api_error($response, -4, 'channel error!');
        exit;
    }

    $gResult->free();
}

$result = $gConn->query("SELECT version_name FROM  t_hw_version_update WHERE version='{$app_vcode}' AND channel={$channel}");

if (!$result) {
    output_json_db_error($response);
    exit;
} else {

    $result_num = $result->num_rows;

    if ($result_num != 1) {
        output_json_register_error2($response, -3, 'app version code error!');
        exit;
    } else {
        $row = $result->fetch_assoc();

        if ($row) {
            $app_vname = $row['version_name'];
        }
    }

    $result->free();
}


/*
if(APPTYPE_SF2CE2022BLUE != $lRegType){
}
 */

$MYIP = get_ip();
//update chip app info
$gConn->query("UPDATE t_hw_chip_list SET last_channel={$channel},app_version='{$app_vcode}',app_version_name='{$app_vname}' WHERE macid='{$real_macid}'");


//insert login info
$result = $gConn->query("SELECT id FROM t_hw_vs_login_info WHERE macid='{$real_macid}' AND event_type = {$LOGIN_EVENT_VERSIONAPI} AND (CURDATE()=LEFT(ltime,10)) LIMIT 1");

if ($result) {

    $result_num = $result->num_rows;

    if (1 == $result_num) {
        $row = $result->fetch_assoc();

        if ($row) {
            $lID = intval($row['id']);
            $gConn->query("UPDATE t_hw_vs_login_info SET ltime=NOW(),ip='{$MYIP}' WHERE id={$lID}");
        }
    } else {
        $gConn->query("INSERT INTO t_hw_vs_login_info (macid,ip,event_type)  VALUES ('{$real_macid}','{$MYIP}',{$LOGIN_EVENT_VERSIONAPI})");
    }

    $result->free();
}

//get max version
$result = $gConn->query("SELECT * FROM t_hw_version_update WHERE version=(SELECT MAX(VERSION) FROM  t_hw_version_update WHERE update_type={$TYPE_ALL_UPDATE} AND channel={$channel}) AND channel={$channel}");

if (!$result) {
    output_json_db_error($response);
    exit;
} else {

    $result_num = $result->num_rows;

    if (1 == $result_num) {
        $row = $result->fetch_assoc();

        if ($row) {
            if ($channel == 11000 && $row['version'] == $app_vcode && !$chipIsExist) {
                // 11000 channel and version match
                output_json_register_error2($response, -2, 'chip id error!');
                exit;
            }
            $response->app_version = $row['version'];
            $response->key_version = $row['key_version'];
            $response->version_name = $row['version_name'];
            $response->size = $row['size'];


            $response->info = $row['info'];
            if (strlen($row['url']) > 0) {
                $response->app_url = $lAES->encrypt($SERVER_RES_PREFIX[$SERVER_REGION_CUR_IDX] . trim($row['url']));
            }
            if (strlen($row['urla']) > 0) {
                $response->app_urla = $lAES->encrypt($SERVER_RES_PREFIX[$SERVER_REGION_CUR_IDX] . trim($row['urla']));
            }
            if (strlen($row['apk_url']) > 0) {
                $response->apk_url = $lAES->encrypt($SERVER_RES_PREFIX[$SERVER_REGION_CUR_IDX] . trim($row['apk_url']));
            }
            $response->enc_file_crc = $row['enc_file_crc'];
            $response->app_file_crc = $row['app_file_crc'];
        }
    }

    $result->free();
}

$result = $gConn->query("SELECT * FROM t_hw_version_update WHERE version=(SELECT MAX(version) FROM  t_hw_version_update WHERE update_type={$TYPE_BETA_UPDATE} AND channel={$channel}) AND channel={$channel}  AND '{$real_macid}'=(SELECT macid cnt FROM t_hw_chip_list WHERE test_machine=1 AND macid='{$real_macid}')");

if (!$result) {
    output_json_db_error($response);
    exit;
} else {

    $result_num = $result->num_rows;

    if (1 == $result_num) {
        $row = $result->fetch_assoc();

        if ($row) {
            $lAppVersion = $row['version'];
            if ($channel == 11000 && $row['version'] == $app_vcode && !$chipIsExist) {
                // 11000 channel and version match
                output_json_register_error2($response, -2, 'chip id error!');
                exit;
            }
            if ($lAppVersion > $response->app_version) {

                $response->app_version = $row['version'];
                $response->key_version = $row['key_version'];
                $response->version_name = $row['version_name'];
                $response->size = $row['size'];


                $response->info = $row['info'];
                if (strlen($row['url']) > 0) {
                    $response->app_url = $lAES->encrypt($SERVER_RES_PREFIX[$SERVER_REGION_CUR_IDX] . trim($row['url']));
                }
                if (strlen($row['urla']) > 0) {
                    $response->app_urla = $lAES->encrypt($SERVER_RES_PREFIX[$SERVER_REGION_CUR_IDX] . trim($row['urla']));
                }
                if (strlen($row['apk_url']) > 0) {
                    $response->apk_url = $lAES->encrypt($SERVER_RES_PREFIX[$SERVER_REGION_CUR_IDX] . trim($row['apk_url']));
                }
                $response->enc_file_crc = $row['enc_file_crc'];
                $response->app_file_crc = $row['app_file_crc'];
            }
        }
    }
    $result->free();
}


// if ($app_vcode == $response->app_version) {

//     $lMsg = '';
//     $lMsgTime = '2020-01-01';
//     $lPushDiff = 0;

//     $result = $gConn->query("SELECT *,TIMESTAMPDIFF(SECOND, now(), updatetime) AS secs FROM  T_system_msg_oneup ORDER BY id DESC LIMIT 1");

//     if ($result) {
//         $result_num = $result->num_rows;

//         if ($result_num == 1) {
//             $row = $result->fetch_assoc();

//             if ($row) {
//                 $lMsg = trim($row['msg']);
//                 $lMsgTime = $row['updatetime'];
//                 $lPushDiff = intval($row['secs']);
//             }
//         }
//         $result->free();
//     }

//     if ($lPushDiff < 0 && strlen($lMsg) > 0) {
//         $result = $gConn->query("SELECT * FROM  T_chip_read_msg_oneup WHERE chipid='{$product_id}'");

//         if ($result) {
//             $result_num = $result->num_rows;

//             if ($result_num == 1) {
//                 $row = $result->fetch_assoc();

//                 if ($row) {
//                     $lReadTime = $row['readtime'];

//                     $lReadTimeTicks = strtotime($lReadTime);
//                     $lMsgTimeTicks = strtotime($lMsgTime);
//                     if ($lMsgTimeTicks > $lReadTimeTicks) {
//                         $response->system_msg = $lMsg;
//                         $gConn->query("UPDATE T_chip_read_msg_oneup SET readtime=now() WHERE chipid='{$product_id}'");
//                     }
//                 }
//             } else {
//                 $response->system_msg = $lMsg;
//                 $gConn->query("INSERT INTO T_chip_read_msg_oneup (chipid)  VALUES ('{$product_id}')");
//             }

//             $result->free();
//         }
//     }
// }

header('Content-type: application/json');
echo json_encode($response);

$gConn->close();
