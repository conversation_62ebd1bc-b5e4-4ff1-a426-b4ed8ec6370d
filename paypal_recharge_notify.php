<?php

include(dirname(__FILE__) . "/games_funs.php");
include(dirname(__FILE__) . '/vendor/autoload.php');
require_once './log.php';

//初始化日志
$logHandler = new CLogFileHandler(sys_get_temp_dir() . DIRECTORY_SEPARATOR . "hw_paypal_notify_" . date('Y-m-d') . '.log');
$log = Log::Init($logHandler, 1);

Log::Debug("++++++ PayPal Start ++++++");

// Webhook事件和头信息
$transmissionId = $_SERVER['HTTP_PAYPAL_TRANSMISSION_ID'];
$transmissionTime = $_SERVER['HTTP_PAYPAL_TRANSMISSION_TIME'];
$certUrl = $_SERVER['HTTP_PAYPAL_CERT_URL'];
$authAlgo = $_SERVER['HTTP_PAYPAL_AUTH_ALGO'];
$transmissionSig = $_SERVER['HTTP_PAYPAL_TRANSMISSION_SIG'];
$webhookEvent = file_get_contents('php://input'); // Webhook事件的原始负载

$webhookEventData = json_decode($webhookEvent, true);

$eventType = $webhookEventData['event_type'];

if ($eventType != 'PAYMENT.CAPTURE.COMPLETED') {
    Log::Debug("Paypal webhook eventType error: " . $eventType);
    // Log::Debug("transmissionId: $transmissionId;transmissionTime: $transmissionTime;certUrl: $certUrl;authAlgo: $authAlgo;transmissionSig: $transmissionSig;webhookEvent: $webhookEvent");
    header("HTTP/1.1 500 Fail");
    exit;
}

// Log::Debug("$webhookEventData");

$gConn = db_connect();


if (!$gConn) {
    Log::Debug("Server database error: " . mysqli_error($gConn));
    Log::Debug("transmissionId: $transmissionId;transmissionTime: $transmissionTime;certUrl: $certUrl;authAlgo: $authAlgo;transmissionSig: $transmissionSig;webhookEvent: $webhookEvent");
    header("HTTP/1.1 500 Fail");
    exit;
}

// get paypal token
$paypalAccessToken = "";
$gResult = $gConn->query("SELECT token_type,access_token FROM t_hw_paypal_access_token ORDER BY id DESC LIMIT 1");
if (!$gResult) {
    Log::Debug("Server database error: " . mysqli_error($gConn));
    Log::Debug("transmissionId: $transmissionId;transmissionTime: $transmissionTime;certUrl: $certUrl;authAlgo: $authAlgo;transmissionSig: $transmissionSig;webhookEvent: $webhookEvent");
    header("HTTP/1.1 500 Fail");
    exit;
} else {
    $lNum = $gResult->num_rows;

    if ($lNum > 0) {
        $row = $gResult->fetch_assoc();

        $token_type = $row['token_type'];
        $access_token = $row['access_token'];
        $paypalAccessToken = "$token_type $access_token";
    } else {
        Log::Debug("Paypal access token not exist.");
        Log::Debug("transmissionId: $transmissionId;transmissionTime: $transmissionTime;certUrl: $certUrl;authAlgo: $authAlgo;transmissionSig: $transmissionSig;webhookEvent: $webhookEvent");
        header("HTTP/1.1 500 Fail");
        exit;
    }

    $gResult->free();
}

// 准备验证请求的负载
$verificationData = json_encode([
    'transmission_id' => $transmissionId,
    'transmission_time' => $transmissionTime,
    'cert_url' => $certUrl,
    'auth_algo' => $authAlgo,
    'transmission_sig' => $transmissionSig,
    'webhook_id' => $PAYPAL_WEBHOOKID,
    'webhook_event' => json_decode($webhookEvent, true),
]);
// 准备请求头
$headers = array(
    'Content-Type' => 'application/json',
    'Authorization' => $paypalAccessToken, // 您从PayPal获取的Access Token
);

$maxRetries = 10; // 最大重试次数
$attempt = 0; // 当前重试次数
while ($attempt < $maxRetries) {
    try {
        // 发送请求
        $response = WpOrg\Requests\Requests::post($PAYPAL_REST_URL . "/v1/notifications/verify-webhook-signature", $headers, $verificationData, array("timeout" => 30, "connect_timeout" => 30));
        // 解析响应
        if ($response->status_code == 200) {
            $responseData = json_decode($response->body, true);
            if (isset($responseData['verification_status']) && $responseData['verification_status'] == 'SUCCESS') {
                // 签名验证成功，处理Webhook事件
                // find order
                // $purchase_units = $webhookEventData->resource->purchase_units;
                $tradeNo = $webhookEventData['resource']['invoice_id'];
                $payPrice = $webhookEventData['resource']['amount']['value'];
                $payCurrency = $webhookEventData['resource']['amount']['currency_code'];

                $total_fee =  intval(strval($payPrice * 100));

                Log::Debug("OK VERIFIED:[{$tradeNo}]");

                $result = $gConn->query("UPDATE t_hw_recharge_trade_no set state=state+1,finish_time=now() WHERE trade_no='" . $tradeNo . "' ");
                if (!$result) {
                    Log::Debug("Server database error: " . mysqli_error($gConn));
                    Log::Debug("transmissionId: $transmissionId;transmissionTime: $transmissionTime;certUrl: $certUrl;authAlgo: $authAlgo;transmissionSig: $transmissionSig;webhookEvent: $webhookEvent");
                    header("HTTP/1.1 500 Fail");
                    exit;
                }

                $gResult = $gConn->query("SELECT * FROM t_hw_recharge_trade_no WHERE trade_no='{$tradeNo}'");
                if (!$gResult) {
                    Log::Debug("Server database error: " . mysqli_error($gConn));
                    Log::Debug("transmissionId: $transmissionId;transmissionTime: $transmissionTime;certUrl: $certUrl;authAlgo: $authAlgo;transmissionSig: $transmissionSig;webhookEvent: $webhookEvent");
                    header("HTTP/1.1 500 Fail");
                    exit;
                }
                $lNum = $gResult->num_rows;
                if ($lNum <= 0) {
                    Log::Debug("+++ trade_no not exist: " . $tradeNo);
                    Log::Debug("transmissionId: $transmissionId;transmissionTime: $transmissionTime;certUrl: $certUrl;authAlgo: $authAlgo;transmissionSig: $transmissionSig;webhookEvent: $webhookEvent");
                    header("HTTP/1.1 500 Fail");
                    exit;
                }
                $row2 = $gResult->fetch_assoc();
                $userID = intval($row2['user_id']);
                $pay_id = intval($row2['pay_id']);
                $tickets_add = intval($row2['tickets']);
                $price = intval($row2['price']);
                $db_currency = trim($row2['currency']);
                $lj_tickets_add = intval($row2['lj_tickets']);
                $state = intval($row2['state']);

                if ($state > 1) {
                    Log::DEBUG("+++ state > 1 ... +++");
                    header("HTTP/1.1 200 OK"); //不要修改或删除
                    exit;
                } else if ($state <= 0) {
                    $result = $gConn->query("UPDATE t_hw_recharge_trade_no set state=1,finish_time=now() WHERE trade_no='" . $tradeNo . "' ");
                    if (!$result) {
                        Log::Debug("Server database error: " . mysqli_error($gConn));
                        Log::Debug("transmissionId: $transmissionId;transmissionTime: $transmissionTime;certUrl: $certUrl;authAlgo: $authAlgo;transmissionSig: $transmissionSig;webhookEvent: $webhookEvent");
                        header("HTTP/1.1 500 Fail");
                        exit;
                    }
                }

                if ($total_fee != $price) {
                    Log::DEBUG('PAYPAL ERROR: pp_pay_price = ' . $total_fee . " hw_price = " . $price);

                    $result_diff = $gConn->query("UPDATE  t_hw_recharge_trade_no set state=-1 WHERE trade_no='" . $tradeNo . "' ");
                    if (!$result_diff) {
                        Log::Debug("Server database error: " . mysqli_error($gConn));
                        header("HTTP/1.1 500 Fail");
                        exit;
                    } else {
                        Log::DEBUG("+++ price diff ... +++");
                        header("HTTP/1.1 200 OK"); //不要修改或删除
                        exit;
                    }
                }

                if (0 != strcasecmp($payCurrency, $db_currency)) {
                    Log::Debug("PAYPAL ERROR:currency diff [$payCurrency] [$db_currency]");

                    $result_diff = $gConn->query("UPDATE  t_hw_recharge_trade_no set state=-2 WHERE trade_no='" . $tradeNo . "' ");
                    if (!$result_diff) {
                        Log::Debug("Server database error: " . mysqli_error($gConn));
                        header("HTTP/1.1 500 Fail");
                        exit;
                    } else {
                        Log::DEBUG("+++ currency diff ... +++");
                        header("HTTP/1.1 200 OK"); //不要修改或删除
                        exit;
                    }
                }

                $lOldLJTickets = 0;
                $lResult =  $gConn->query("SELECT * FROM t_hw_users_package WHERE user_id = {$userID}");
                if (!$lResult) {
                    Log::Debug("Server database error: " . mysqli_error($gConn));
                    Log::Debug("transmissionId: $transmissionId;transmissionTime: $transmissionTime;certUrl: $certUrl;authAlgo: $authAlgo;transmissionSig: $transmissionSig;webhookEvent: $webhookEvent");
                    header("HTTP/1.1 500 Fail");
                    exit;
                } else {
                    $lNum = $lResult->num_rows;

                    if (1 != $lNum) {
                        Log::DEBUG("+++ Get user package error +++");
                        Log::Debug("transmissionId: $transmissionId;transmissionTime: $transmissionTime;certUrl: $certUrl;authAlgo: $authAlgo;transmissionSig: $transmissionSig;webhookEvent: $webhookEvent");
                        header("HTTP/1.1 500 Fail");
                        exit;
                    } else {
                        $lRow = $lResult->fetch_assoc();

                        if (!$lRow) {
                            Log::DEBUG("+++ Get user package error2 +++");
                            Log::Debug("transmissionId: $transmissionId;transmissionTime: $transmissionTime;certUrl: $certUrl;authAlgo: $authAlgo;transmissionSig: $transmissionSig;webhookEvent: $webhookEvent");
                            header("HTTP/1.1 500 Fail");
                            exit;
                        } else {
                            $lOldLJTickets = intval($lRow['total_gold']);
                        }
                    }

                    $lResult->free();
                }

                $lMysqlCMD = "UPDATE t_hw_users_package SET gold=gold+{$tickets_add},total_gold=total_gold+{$lj_tickets_add} WHERE user_id={$userID} ";
                $result = $gConn->query($lMysqlCMD);
                if (!$result) {
                    Log::Debug("Server database error: " . mysqli_error($gConn));
                    Log::Debug("transmissionId: $transmissionId;transmissionTime: $transmissionTime;certUrl: $certUrl;authAlgo: $authAlgo;transmissionSig: $transmissionSig;webhookEvent: $webhookEvent");
                    header("HTTP/1.1 500 Fail");
                    exit;
                }

                //[BUSINESS_STATE_FINISH]
                $VAR_BUSINESS_STATE_FINISH = BUSINESS_STATE_FINISH;
                $gConn->query("UPDATE t_hw_recharge_trade_no SET business_state={$VAR_BUSINESS_STATE_FINISH} WHERE trade_no='{$tradeNo}' ");
                //----------------------------

                if ($lOldLJTickets <= 0 && $lj_tickets_add > 0) {
                    $gConn->query("UPDATE t_hw_recharge_trade_no SET isnew=1 WHERE trade_no='{$tradeNo}' ");
                }

                Log::DEBUG("+++ Success ... +++");
            } else {
                // 签名验证失败，记录或处理错误
                Log::Debug("Verify paypal sign error.");
                Log::Debug("transmissionId: $transmissionId;transmissionTime: $transmissionTime;certUrl: $certUrl;authAlgo: $authAlgo;transmissionSig: $transmissionSig;webhookEvent: $webhookEvent");
                header("HTTP/1.1 500 Fail");
                exit;
            }
        } else {
            // 签名验证失败，记录或处理错误
            Log::Debug("Verify paypal sign request error, status_code: " . $response->status_code . ", $response->body");
            Log::Debug("transmissionId: $transmissionId;transmissionTime: $transmissionTime;certUrl: $certUrl;authAlgo: $authAlgo;transmissionSig: $transmissionSig;webhookEvent: $webhookEvent");
            header("HTTP/1.1 500 Fail");
            exit;
        }
        break;
    } catch (WpOrg\Requests\Exception $e) {
        // 捕获其他异常，可能需要根据实际情况处理或直接抛出
        Log::Debug("Verify signature request exception: " . $e);

        // 重发请求
        $attempt++;
        if ($attempt == $maxRetries) {
            Log::Debug("Verify signature request limit.");
            header("HTTP/1.1 500 Fail");
            exit;
        }
    }
}



header("HTTP/1.1 200 OK");
