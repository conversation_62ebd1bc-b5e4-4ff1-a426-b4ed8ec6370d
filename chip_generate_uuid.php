<?php

include(dirname(__FILE__) . "/games_funs.php");
require 'vendor/autoload.php';

use <PERSON>\Uuid\Uuid;

//---------------- JSON DATA CLASS ---------------------------

class GLResponse
{
    public $msg = 'ok';
    public $ret = 0;

    public $uuid = '';
};


$response = new GLResponse();

$product_id = $_POST['device_id'];
$channel = $_POST['channel'];

$token = $_POST['token'];


if (NULL == $product_id || NULL == $channel || NULL == $token) {
    output_json_para_error($response);
    exit;
}

$product_id = trim($product_id);
$channel = intval($channel);
$token = trim($token);

if (sha1($product_id . $channel . $VS_KEY) != $token) {
    output_json_token_error($response);
    exit;
}

//--------------------------------------------------------

//connect database and query
$gConn = db_connect();

if (!$gConn) {
    output_json_db_error($response);
    exit;
}

$MYIP = get_ip();

$product_id = mysqli_real_escape_string($gConn, $product_id);

$gResult = $gConn->query("SELECT channel FROM t_hw_all_channel WHERE channel={$channel}");
if (!$gResult) {
    output_json_db_error($response);
    exit;
} else {
    $lNum = $gResult->num_rows;

    if ($lNum != 1) {
        output_json_api_error($response, -3, 'channel error!');
        exit;
    }

    $gResult->free();
}

$real_chipid = '';

$gResult = $gConn->query("SELECT macid FROM t_hw_chip_list WHERE macid_enc='{$product_id}'");
if (!$gResult) {
    output_json_db_error($response);
    exit;
} else {
    $result_num = $gResult->num_rows;

    if ($result_num > 0) {
        $row = $gResult->fetch_assoc();

        $real_chipid = $row['macid'];
    } else {
        output_json_api_error($response, -2, 'chip id error!');
        exit;
    }

    $gResult->free();
}


$unique = false;
while(!$unique) {
    $uuid = Uuid::uuid4()->toString();
    $gResult = $gConn->query("SELECT uuid FROM t_hw_chip_uudi_record WHERE uuid='{$uuid}'");
    if (!$gResult) {
        output_json_db_error($response);
        exit;
    } else {
        $result_num = $gResult->num_rows;

        if ($result_num == 0) {
            $unique = true;
            $lResult = $gConn->query("INSERT INTO t_hw_chip_uudi_record (uuid, chipid, channel, ip, create_time) VALUES ('{$uuid}', '{$real_chipid}', {$channel}, '{$MYIP}', NOW())");
            if (!$lResult) {
                output_json_db_error($response);
                exit;
            }
            $response->uuid = $uuid;
        } 
    }
}

header('Content-type: application/json');
echo json_encode($response);

$gConn->close();



