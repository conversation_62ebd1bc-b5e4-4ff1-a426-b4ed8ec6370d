<?php

include(dirname(__FILE__) . "/games_funs.php");
include(dirname(__FILE__) . "/log.php");

require_once __DIR__ . '/vendor/autoload.php';

use OSS\OssClient;
use OSS\Core\OssException;
use <PERSON>\Uuid\Uuid;

//初始化日志
$logHandler = new CLogFileHandler(sys_get_temp_dir() . DIRECTORY_SEPARATOR . "gm_wxmini_get_login_qrcode.log");
$log = Log::Init($logHandler, 1);

$QRCODE_BASE_URL = "https://arcade-app-res.oss-cn-hangzhou.aliyuncs.com/qrcode/";

class GLResponse
{

  public $msg = 'ok';
  public $ret = 0;

  public $qrcode_url = '';
  public $series_no = '';
};

$response = new GLResponse();

//-----------------------------------------------------------

$product_id = $_GET['device_id'];
$channel = $_GET['channel'];
$token = $_GET['token'];



//-----------------------------------------------------------

if ($product_id == NULL || $channel == NULL || $token == NULL) {
  output_json_para_error($response);
  exit;
}

$product_id = trim($product_id);
$channel = intval($channel);
$token = trim($token);

if (sha1($product_id . $channel . $VS_KEY) != $token) {
  output_json_token_error($response);
  exit;
}

//connect database and query
$gConn = db_connect();

if (!$gConn) {
  output_json_db_error($response);
  exit;
}

$product_id = mysqli_real_escape_string($gConn, $product_id);

$real_macid = '';
$result = $gConn->query("SELECT macid FROM  t_hw_chip_list WHERE macid_enc='{$product_id}'");
if (!$result) {
  output_json_db_error($response);
  exit;
} else {

  $result_num = $result->num_rows;

  if ($result_num != 1) {
    Log::DEBUG("Error chipid: " . $chip_id);
    output_json_register_error2($response, -2, 'chip id error!');
    exit;
  } else {
    $row = $result->fetch_assoc();

    if ($row) {
      $real_macid = $row['macid'];
    }
  }

  $result->free();
}
$gResult = $gConn->query("SELECT channel FROM t_hw_all_channel WHERE channel={$channel}");
if (!$gResult) {
  output_json_db_error($response);
  exit;
} else {
  $lNum = $gResult->num_rows;

  if ($lNum != 1) {
    Log::DEBUG("Error channel: " . $channel);
    output_json_api_error($response, -4, 'channel error!');
    exit;
  }

  $gResult->free();
}

$access_token = '';
$gResult = $gConn->query("SELECT access_token FROM t_wxmini_access_token ORDER BY id DESC LIMIT 1");
if (!$gResult) {
  Log::DEBUG(mysqli_error($gConn));
  output_json_db_error($response);
  exit;
} else {
  $lNum = $gResult->num_rows;

  if ($lNum > 0) {
    $row = $gResult->fetch_assoc();

    $access_token = $row['access_token'];
  } else {
    Log::DEBUG("no access_token in db.");
    output_json_register_error2($response, -3, 'get qrcode error!');
    exit;
  }
}

if (strlen($access_token) == 0) {
  Log::DEBUG("no access_token in db.");
  output_json_register_error2($response, -3, 'get qrcode error!');
  exit;
}

// 获取微信小程序二维码
$series_no = substr(bin2hex(random_bytes(10)), 0, 10) . random_int(1, 99999999) . getmypid();
$scene = "m=1&s=" . $series_no;
$postData = array(
  'scene' => $scene,
  'check_path' => false,
  'page' => 'pages/index/index',
  'env_version' => 'release'
);
$postJson = json_encode($postData);
try {
  $miniCodeResponse = \WpOrg\Requests\Requests::post('https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=' . $access_token, array(), $postJson);
} catch (WpOrg\Requests\Exception $e) {
  Log::Debug("Request exception: " . $e);
  output_json_register_error2($response, -3, 'get qrcode error!');
  exit;
}
if ($miniCodeResponse->status_code != 200) {
  Log::DEBUG('wxmini qrcode request error: ' . $miniCodeResponse->status_code);
  output_json_register_error2($response, -3, 'get qrcode error!');
  exit;
}
$miniCodeResponseData = json_decode($miniCodeResponse->body, true);
if (isset($miniCodeResponseData['errcode']) && $miniCodeResponseData['errcode'] != 0) {
  Log::DEBUG('get wxmini qrcode error: ' . $miniCodeResponseData['errcode'] . ', ' . $miniCodeResponseData['errmsg']);
  exit;
}

$buffer = $miniCodeResponse->body;

// 将返回的buffer流存储为图片
$uuid = Uuid::uuid4()->toString();
$imageName = "wxmini_qrcode_" . $uuid . ".png";
$filePath = realpath(__DIR__) . '/uploads/qrcodes/';
if (!file_exists($filePath)) {
  mkdir($filePath, 0777, true);  // 如果目录不存在则创建
}
// 将 buffer 写入文件
$imagePath = $filePath . $imageName;
file_put_contents($imagePath, $buffer);
// 检查文件是否成功保存
if (!file_exists($filePath)) {
  Log::DEBUG("图片保存失败。");
  exit;
}

putenv('HTTP_PROXY');
putenv('HTTPS_PROXY');
putenv('http_proxy');
putenv('https_proxy');

// 上传到阿里云OSS

// $endpoint = "oss-cn-hangzhou-internal.aliyuncs.com";
$endpoint = "oss-cn-hangzhou.aliyuncs.com";
$bucket = "arcade-app-res";
$object = "qrcode/" . $imageName;

// 上传时可以设置相关的headers，例如设置访问权限为private、自定义元数据等。
try {
  $ossClient = new OssClient(OSS_ACCESS_KEY_ID, OSS_ACCESS_KEY_SECRET, ALIOSS_ENDPOINT);

  $oss_response = $ossClient->uploadFile($bucket, $object, $imagePath);
  if ($oss_response && $oss_response['info']['http_code'] == 200) {
  } else {
    Log::DEBUG('upload qrcode to oss error: ' . $oss_response['info']['http_code']);
    unlink($imagePath);
    output_json_register_error2($response, -3, 'get qrcode error!');
    exit;
  }
} catch (OssException $e) {
  Log::DEBUG('upload qrcode to oss error: ' . $e->getMessage());
  unlink($imagePath);
  output_json_register_error2($response, -3, 'get qrcode error!');
  exit;
}

unlink($imagePath);

$now = date('Y-m-d H:i:s');
$qrcode_url = $QRCODE_BASE_URL . $imageName;
$result = $gConn->query("INSERT INTO t_wxmini_qrcode (chip_id, app_type, channel, series_no, qrcode_url, create_time) VALUES ('{$real_macid}', {$GLOBAL_MATCH_APP}, {$channel}, '{$series_no}', '{$qrcode_url}', '{$now}')");
if (!$result) {
  output_json_db_error($response);
  exit;
}

$response->series_no = $series_no;
$response->qrcode_url = $qrcode_url;

header('Content-type: application/json');
echo json_encode($response);

$gConn->close();
