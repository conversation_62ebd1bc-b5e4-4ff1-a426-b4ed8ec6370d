<?php

require_once 'file_encrypt_decrypt.php';

echo "文件加解密测试脚本\n";
echo "==================\n\n";

// 测试参数
$key = 'cH7nI2oM7xB1oF8k';  // 16字节密钥
$method = 'AES-128-ECB';
$testFile = 'test_data.txt';
$encryptedFile = "D:\xiaoyuan\yz_handgame\EN12001_V1.2.1.zip";
$decryptedFile = "EN12001_V1.2.1.zip";

// 创建测试数据文件
$testData = "这是一个测试文件的内容。\n包含中文和英文字符。\nThis is a test file content.\nIt contains Chinese and English characters.\n数字: 123456789\n特殊字符: !@#$%^&*()";

// echo "1. 创建测试文件...\n";
// file_put_contents($testFile, $testData);
// echo "测试文件创建成功: $testFile\n";
// echo "原始内容长度: " . strlen($testData) . " bytes\n\n";

// // 创建加解密实例
$fileEncrypt = new FileEncryptDecrypt($key, $method);

// echo "2. 测试文件加密...\n";
// $result = $fileEncrypt->encryptFile($testFile, $encryptedFile);
// if ($result) {
//     echo "加密测试通过！\n\n";
// } else {
//     echo "加密测试失败！\n\n";
//     exit(1);
// }

echo "3. 测试文件解密...\n";
$result = $fileEncrypt->decryptFile($encryptedFile, $decryptedFile);
if ($result) {
    echo "解密测试通过！\n\n";
} else {
    echo "解密测试失败！\n\n";
    exit(1);
}

// echo "4. 验证解密结果...\n";
// $originalData = file_get_contents($testFile);
// $decryptedData = file_get_contents($decryptedFile);

// if ($originalData === $decryptedData) {
//     echo "✓ 解密结果与原始数据完全一致！\n";
//     echo "验证通过！\n\n";
// } else {
//     echo "✗ 解密结果与原始数据不一致！\n";
//     echo "验证失败！\n\n";
//     exit(1);
// }

// echo "5. 测试批量加密...\n";
// // 创建测试目录和文件
// $inputDir = 'test_input';
// $outputDir = 'test_output';
// $decryptDir = 'test_decrypt';

// if (!is_dir($inputDir)) {
//     mkdir($inputDir, 0755, true);
// }

// // 创建多个测试文件
// for ($i = 1; $i <= 3; $i++) {
//     $content = "测试文件 $i 的内容\nTest file $i content\n数据: " . rand(1000, 9999);
//     file_put_contents($inputDir . "/test$i.txt", $content);
// }

// // 批量加密
// $successCount = $fileEncrypt->encryptDirectory($inputDir, $outputDir, 'txt');
// echo "批量加密完成，成功处理 $successCount 个文件\n\n";

// echo "6. 测试批量解密...\n";
// // 批量解密
// $successCount = $fileEncrypt->decryptDirectory($outputDir, $decryptDir);
// echo "批量解密完成，成功处理 $successCount 个文件\n\n";

// echo "7. 验证批量处理结果...\n";
// $allMatch = true;
// for ($i = 1; $i <= 3; $i++) {
//     $originalFile = $inputDir . "/test$i.txt";
//     $decryptedFile = $decryptDir . "/test$i.txt";
    
//     if (file_exists($originalFile) && file_exists($decryptedFile)) {
//         $original = file_get_contents($originalFile);
//         $decrypted = file_get_contents($decryptedFile);
        
//         if ($original === $decrypted) {
//             echo "✓ test$i.txt 验证通过\n";
//         } else {
//             echo "✗ test$i.txt 验证失败\n";
//             $allMatch = false;
//         }
//     } else {
//         echo "✗ test$i.txt 文件缺失\n";
//         $allMatch = false;
//     }
// }

// if ($allMatch) {
//     echo "\n所有测试通过！文件加解密功能正常工作。\n";
// } else {
//     echo "\n部分测试失败！请检查代码。\n";
// }

// echo "\n8. 清理测试文件...\n";
// // 清理测试文件
// $filesToClean = [
//     $testFile,
//     $encryptedFile,
//     $decryptedFile
// ];

// foreach ($filesToClean as $file) {
//     if (file_exists($file)) {
//         unlink($file);
//         echo "删除: $file\n";
//     }
// }

// 清理测试目录
function deleteDirectory($dir) {
    if (!is_dir($dir)) {
        return;
    }
    
    $files = array_diff(scandir($dir), array('.', '..'));
    foreach ($files as $file) {
        $path = $dir . DIRECTORY_SEPARATOR . $file;
        if (is_dir($path)) {
            deleteDirectory($path);
        } else {
            unlink($path);
        }
    }
    rmdir($dir);
}

// deleteDirectory($inputDir);
// deleteDirectory($outputDir);
// deleteDirectory($decryptDir);

// echo "清理完成！\n";
echo "\n测试脚本执行完毕。\n";

?>
