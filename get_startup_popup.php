<?php

include(dirname(__FILE__) . "/games_funs.php");

//---------------- JSON DATA CLASS ---------------------------

class GLResponse
{
    public $msg = 'ok';
    public $ret = 0;

    public $popup_list = NULL;
};

class PopupItem
{

    public $id = 0;
    public $content = ''; // 图片url/文本内容/视频url/网页url/
    public $type = 0; // 0: 图片弹窗  1: 文本弹窗  2: 视频弹窗  3: 网页弹窗 
    public $duration = 0; // 每次展示时间(秒，保留参数，暂时不用) 
    public $show_times = 0; // 启动后展示次数(保留参数，暂时不用)
}

$response = new GLResponse();
//-----------------------------------------------------------

$product_id = $_GET['device_id'];
$lang = $_GET['lang'];
$channel = $_GET['channel'];
$token = $_GET['token'];

if ($product_id == NULL || $lang == NULL || $channel == NULL || $token == NULL) {
    output_json_para_error($response);
    exit;
}

$product_id = trim($product_id);
$lang = intval($lang);
$channel = intval($channel);
$token = trim($token);

if (sha1($product_id . $lang . $channel . $VS_KEY) != $token) {
    output_json_token_error($response);
    exit;
}

$language_id = $gDLangTSuffix;
if (isset($gAllLanguage[$lang])) {
    $language_id = $lang;
}

//-----------------------------------------------------------

$gConn = db_connect();

if (!$gConn) {
    output_json_db_error($response);
    exit;
}

$product_id = mysqli_real_escape_string($gConn, $product_id);

if (!is_chip_id_exist($gConn, $response, $product_id)) {
    exit;
}

$result = $gConn->query("SELECT id,content,type FROM t_hw_popup_list WHERE start_time<=now() AND end_time>now() AND is_show=1 ORDER BY id DESC");
if (!$result) {
    output_json_db_error($response);
    exit;
} else {
    $result_num = $result->num_rows;
    for ($i = 0; $i < $result_num; $i++) {
        $row = $result->fetch_assoc();

        $lItem = new PopupItem();
        $lItem->id =  intval($row['id']);
        $lItem->type = intval($row['type']);
        $lItem->content = trim($row['content']);
        $response->popup_list[] = $lItem;
    }
    $result->free();
}

//-----------------------------------------------------------
header('Content-type: application/json');
echo json_encode($response);

$gConn->close();


