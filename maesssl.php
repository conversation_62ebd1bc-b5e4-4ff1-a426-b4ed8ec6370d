<?php

class AESSSL {
    /**
     * var string $method 加解密方法，可通过openssl_get_cipher_methods()获得
     */
    protected $method;

    /**
     * var string $secret_key 加解密的密钥
     */
    protected $secret_key;

    /**
     * var string $iv 加解密的向量，有些方法需要设置比如CBC
     */
    protected $iv;

    /**
     * var string $options 
     * options 参数即为重要，它是兼容 mcrpty 算法的关键：

     * options = 0: 默认模式，自动对明文进行 pkcs7 padding，且数据做 base64 编码处理。
     * options = 1: OPENSSL_RAW_DATA，自动对明文进行 pkcs7 padding， 且数据未经 base64 编码处理。
     * options = 2: OPENSSL_ZERO_PADDING，要求待加密的数据长度已按 "0" 填充与加密算法数据块长度对齐，即同 mcrpty 默认填充的方式一致，且对数据做 base64 编码处理。注意，此模式下 openssl 要求待加密数据已按 "0" 填充好，其并不会自动帮你填充数据，如果未填充对齐，则会报错。
     */
    protected $options;

    /**
     * 构造函数
     *
     * @param string $key 密钥
     * @param string $method 加密方式
     * @param string $iv iv向量
     * @param mixed $options 还不是很清楚
     *
     */
    public function __construct($key, $method = 'AES-128-ECB', $iv = '', $options = 0) {
        // key是必须要设置的
        $this->secret_key = isset($key) ? $key : '1234567890';

        $this->method = $method;

        $this->iv = $iv;

        $this->options = $options;
    }

    /**
     * 加密方法，对数据进行加密，返回加密后的数据
     *
     * @param string $data 要加密的数据
     *
     * @return string
     *
     */
    public function encrypt($data) {
        return openssl_encrypt($data, $this->method, $this->secret_key, $this->options, $this->iv);
    }

    /**
     * 解密方法，对数据进行解密，返回解密后的数据
     *
     * @param string $data 要解密的数据
     *
     * @return string
     *
     */
    public function decrypt($data) {
        return openssl_decrypt($data, $this->method, $this->secret_key, $this->options, $this->iv);
    }
}
