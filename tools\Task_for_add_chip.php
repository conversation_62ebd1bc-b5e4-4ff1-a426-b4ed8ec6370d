<?php

ob_implicit_flush(1);
ob_end_flush();

include(dirname(__FILE__) . "/../games_funs.php");

class GLResponse
{
    public $msg = 'ok';
    public $ret = 0;

    public $success = 0;
    public $fail = 0;
};

$securite_key = $_POST['securite_key'];
$startChip = $_POST['startChip'];
$endChip = $_POST['endChip'];
$channel = $_POST['channel'];
$addNum = $_POST['addNum'];
$calcType = $_POST['calcType'];

if ($securite_key == NULL || $startChip == NULL || $endChip === NULL || $channel === NULL || $addNum === null || $calcType === NULL || $securite_key != '2OWSXYsZCMMU0GLd') {
    output_json_para_error($response);
    exit;
} else {
    $startChip = trim($startChip);
    $endChip = trim($endChip);
    $channel = trim($channel);
    $addNum = intval($addNum);
    $calcType = intval($calcType);
}

//--------------------------------------------------------

//connect database and query
try {
    $gConn = db_connect();
} catch (\Throwable $th) {
    output_json_db_error($response);
    exit;
}


// 序列号生成逻辑
$chipArray = array();

// 提取前6位标识头和后面的序列号部分
$prefix = substr($startChip, 0, 6); // 例如: E6OP01
$startSerial = substr($startChip, 6); // 例如: 000000726

// 根据endChip或addNum确定生成数量
$generateCount = 0;
if (!empty($endChip)) {
    $endSerial = substr($endChip, 6);
    if ($calcType == 0) {
        // 十六进制计算
        $startNum = hexdec($startSerial);
        $endNum = hexdec($endSerial);
        $generateCount = $endNum - $startNum + 1;
    } else {
        // 十进制计算
        $startNum = intval($startSerial);
        $endNum = intval($endSerial);
        $generateCount = $endNum - $startNum + 1;
    }
} else if ($addNum > 0) {
    $generateCount = $addNum;
} else {
    output_json_para_error($response);
    exit;
}
// 生成序列号数组
if ($calcType == 0) {
    // 十六进制加法
    $currentNum = hexdec($startSerial);
    for ($i = 0; $i < $generateCount; $i++) {
        $serialPart = strtoupper(str_pad(dechex($currentNum + $i), strlen($startSerial), '0', STR_PAD_LEFT));
        $chipArray[] = $prefix . $serialPart;
    }
} else {
    // 十进制加法
    $currentNum = intval($startSerial);
    for ($i = 0; $i < $generateCount; $i++) {
        $serialPart = str_pad($currentNum + $i, strlen($startSerial), '0', STR_PAD_LEFT);
        $chipArray[] = $prefix . $serialPart;
    }
}

$channelArr = explode(",", $channel);
$default_channel = intval($channelArr[0]);
foreach ($chipArray as $key => $lChipID) {
    $lChipID = strtoupper($lChipID);
    $lChipID_MD5 = md5($lChipID);
    $lSalt = generateUniqueSalt();
    try {
        $gConn->query("INSERT INTO t_hw_chip_list (chipid, chipid_enc, macid, macid_enc, default_channel, finger_regnum, finger_salt, allow_channel) VALUES('{$lChipID}','{$lChipID_MD5}','{$lChipID}','{$lChipID_MD5}', {$default_channel}, 0, '{$lSalt}', '{$channel}')");
    } catch (\Throwable $th) {
        
    }
}