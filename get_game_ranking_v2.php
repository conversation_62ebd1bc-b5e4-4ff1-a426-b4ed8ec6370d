<?php

include(dirname(__FILE__) . "/games_funs.php");

include(dirname(__FILE__) . "/player_common_struct.php");

//---------------- JSON DATA CLASS ---------------------------
// 星势力版服务器排行榜接口

class GLResponse
{

    public $msg = 'ok';
    public $ret = 0;

    public $data = NULL;
};

class ReturnData
{

    public $game_id = 0;
    public $content_type = 0;
    public $vsGameInfo = NULL;
    public $myVsGameInfo = NULL;
    public $coopGameInfo = NULL;
    public $myCoopGameInfo = NULL;
}


class VSGameInfo
{
    public $icon_url = '';
    public $nickname = '';
    public $level = 1;
    public $rankLevel = 1;
    public $levelExp = 0;
    public $levelTotalExp = 500;
    public $rank = 0;
}

class MyVSGameInfo
{
    public $icon_url = '';
    public $nickname = '';
    public $level = 1;
    public $rankLevel = 1;
    public $levelExp = 0;
    public $levelTotalExp = 500;
    public $rank = 0;
}

class CoopGameInfo
{
    public $icon_url = '';
    public $nickname = '';
    public $level = 1;
    public $rankLevel = 1;
    public $levelExp = 0;
    public $levelTotalExp = 500;
    public $rank = 0;
}

class MyCoopGameInfo
{
    public $icon_url = '';
    public $nickname = '';
    public $level = 1;
    public $rankLevel = 1;
    public $levelExp = 0;
    public $levelTotalExp = 500;
    public $rank = 0;
}


//-----------------------------------------------------------

$response = new GLResponse();
$returnData = new ReturnData();

//args
@$user_id = $_GET['user_id'];
@$login_token = $_GET['login_token'];

$game_id = $_GET['game_id'];

$token = $_GET['token'];

/*
$user_id = 8;
$login_token = 'e33ae3984260e1b1a8dfbf8af86f509c0a3b162e';
$product_id = 'aaa';

$game_id = 0;

$token = 'aaaaa';
 */

if (
    NULL === $user_id
    || NULL === $login_token
    || NULL === $game_id
    || NULL === $token
) {
    output_json_para_error($response);
    exit;
}

$user_id = intval($user_id);
$login_token = trim($login_token);

$game_id = intval($game_id);

$token = trim($token);

if (sha1($user_id . $login_token . $game_id . $VS_KEY) != $token) {
    output_json_token_error($response);
    exit;
}

$gConn = db_connect();

if (!$gConn) {
    output_json_db_error($response);
    exit;
}

$login_token = mysqli_real_escape_string($gConn, $login_token);

$verifyUserFlag = false;
if ($user_id > 0 && strlen($login_token) > 0) {
    //check login token
    if (!is_login_token_valid($gConn, $response, $user_id, $login_token)) {
        exit;
    } else {
        $verifyUserFlag = true;
    }
}


$gContentType = 0;
$lResult = $gConn->query("SELECT content_type FROM t_game_vs_jj_3 WHERE id={$game_id} AND has_records=1");
if (!$lResult) {
    output_json_db_error($response);
    exit;
} else {
    $lNum = $lResult->num_rows;

    if (1 != $lNum) {
        output_json_register_error2($response, -4, 'Game no rank!');
        exit;
    } else {
        $lRow = $lResult->fetch_assoc();
        $gContentType = intval($lRow['content_type']);
    }
}

//--------------------------------------------------------------------------------

$MAXRANKNUM = 100;

$gUsers = [];
$lCurUserIDArray = [];
$vsGameRank = array();
$coopGameRank = array();
$myDataExist = false;
if (0 == $gContentType) {
    $lResult = $gConn->query("SELECT ta.user_id,ta.level,ta.points,tb.icon_url,tb.nickname FROM t_hw_user_scores_game_{$game_id} AS ta INNER JOIN t_all_gameusers AS tb ON ta.user_id=tb.user_id WHERE ta.points>0 ORDER BY ta.points DESC LIMIT {$MAXRANKNUM} ");

    if (!$lResult) {
        output_json_db_error($response);
        exit;
    } else {
        $lNum = $lResult->num_rows;

        for ($lIDX = 0; $lIDX < $lNum; $lIDX++) {
            //fill response
            $lRow = $lResult->fetch_assoc();

            $lItem = new VSGameInfo();

            $lCurUserID = intval($lRow['user_id']);

            // $gUsers[$lCurUserID] = new PlayerGameInfo();
            // $gUsers[$lCurUserID]->vsGameInfo = new VSGameInfo();

            // $gUsers[$lCurUserID]->vsGameInfo->win = intval($lRow['win']);
            // $gUsers[$lCurUserID]->vsGameInfo->lose = intval($lRow['lose']);
            // $gUsers[$lCurUserID]->vsGameInfo->draw = intval($lRow['draw']);
            // $gUsers[$lCurUserID]->vsGameInfo->escape = intval($lRow['escape']);

            $lLevel = intval($lRow['level']);
            $lPoints = intval($lRow['points']);

            // $lItem->id = $lIDX;
            $lItem->icon_url = $lRow['icon_url'];
            $lItem->nickname = $lRow['nickname'];
            $lItem->level = intval($lRow['level']);
            // $lItem->points = intval($lRow['points']);
            $lItem->rankLevel = getRankLevelFromLevel($lLevel);
            getGDLevelExpFromPL($lPoints, $lLevel, $lItem->levelTotalExp, $lItem->levelExp);
            $lItem->rank = $lIDX + 1;
            // $lItem->rank_level = getRankLevelFromLevel($lItem->level);
            //------------------------------------------------------------
            // $gUsers[$lCurUserID]->vsGameInfo->level = $lLevel;
            // $gUsers[$lCurUserID]->vsGameInfo->rankLevel = getRankLevelFromLevel($lLevel);
            // getGDLevelExpFromPL($lPoints, $lLevel, $gUsers[$lCurUserID]->vsGameInfo->levelTotalExp, $gUsers[$lCurUserID]->vsGameInfo->levelExp);

            // $lCurUserIDArray[] = $lCurUserID;


            if ($lCurUserID == $user_id && $verifyUserFlag) {
                $lMyItem = new MyVSGameInfo();
                $lMyItem->icon_url = $lRow['icon_url'];
                $lMyItem->nickname = $lRow['nickname'];
                $lMyItem->level = intval($lRow['level']);
                $lMyItem->rankLevel = getRankLevelFromLevel($lLevel);
                $lMyItem->rank = $lIDX + 1;
                getGDLevelExpFromPL($lPoints, $lLevel, $lMyItem->levelTotalExp, $lMyItem->levelExp);
                $returnData->myVsGameInfo = $lMyItem;
                $myDataExist = true;
            }

            $vsGameRank[] = $lItem;
        }

        if (count($vsGameRank) > 0) {
            $returnData->vsGameInfo = $vsGameRank;
        }

        $lResult->free();
    }
} else if (1 == $gContentType) {
    $lResult = $gConn->query("SELECT ta.user_id,ta.points,ta.level,tb.nickname,tb.icon_url FROM (SELECT user_id,level,points,single_tg_num,multi_tg_num FROM t_hw_user_scores_coop_{$game_id} ORDER BY level DESC LIMIT 150) AS ta INNER JOIN t_all_gameusers AS tb ON ta.user_id=tb.user_id ORDER BY level DESC, single_tg_num DESC,multi_tg_num DESC LIMIT 100");
    if (!$lResult) {
        output_json_db_error($response);
        exit;
    } else {
        $lNum = $lResult->num_rows;

        for ($i = 0; $i < $lNum; $i++) {
            $lRow = $lResult->fetch_assoc();
            // $lUserID = intval($lRow['user_id']);
            $lLevel = intval($lRow['level']);
            $lPoints = intval($lRow['points']);

            $lItem = new CoopGameInfo();

            $lCurUserID = intval($lRow['user_id']);

            //$lItem->playerInfo = new PlayerSubInfo();
            //$lItem->playerInfo->userID = $lUserID;

            $lLevelTotalExp = 0;
            $lLevelExp = 0;

            // $lItem->id = $i;
            $lItem->level = intval($lRow['level']);
            // $lItem->points = intval($lRow['points']);
            $lItem->icon_url = $lRow['icon_url'];
            $lItem->nickname = $lRow['nickname'];
            $lItem->rank = $i + 1;
            $lItem->rankLevel = getRankLevelFromLevel($lLevel);
            getGDLevelExpFromPL($lPoints, $lLevel, $lItem->levelTotalExp, $lItem->levelExp);
            // $lItem->rankLevel = getRankLevelFromLevel($lLevel);
            // $lItem->levelExp = $lLevelExp;
            // $lItem->levelTotalExp = $lLevelTotalExp;

            if ($lCurUserID == $user_id && $verifyUserFlag) {
                $lMyItem = new MyCoopGameInfo();
                $lMyItem->icon_url = $lRow['icon_url'];
                $lMyItem->nickname = $lRow['nickname'];
                $lMyItem->level = intval($lRow['level']);
                // $lMyItem->points = intval($lRow['points']);
                $lMyItem->rank = $i + 1;
                $lMyItem->rankLevel = getRankLevelFromLevel($lLevel);
                getGDLevelExpFromPL($lPoints, $lLevel, $lMyItem->levelTotalExp, $lMyItem->levelExp);
                $returnData->myCoopGameInfo = $lMyItem;
                $myDataExist = true;
            }

            $coopGameRank[] = $lItem;
        }

        if (count($coopGameRank) > 0) {
            $returnData->coopGameInfo = $coopGameRank;
        }

        $lResult->free();
    }
}

// 查询我的战绩
if (!$myDataExist && $verifyUserFlag) {
    if (0 == $gContentType) {
        $lResult = $gConn->query("SELECT ta.user_id,ta.level,ta.points,tb.icon_url,tb.nickname FROM t_hw_user_scores_game_{$game_id} AS ta INNER JOIN t_all_gameusers AS tb ON ta.user_id=tb.user_id WHERE ta.user_id={$user_id}");
        if ($lResult) {
            $lNum = $lResult->num_rows;

            if ($lNum == 1) {
                $lRow = $lResult->fetch_assoc();

                $lLevel = intval($lRow['level']);
                $lPoints = intval($lRow['points']);

                $lMyItem = new MyVSGameInfo();
                $lMyItem->icon_url = $lRow['icon_url'];
                $lMyItem->nickname = $lRow['nickname'];
                $lMyItem->level = intval($lRow['level']);
                // $lMyItem->points = intval($lRow['points']);
                $lMyItem->rank = 0;
                $lMyItem->rankLevel = getRankLevelFromLevel($lLevel);
                getGDLevelExpFromPL($lPoints, $lLevel, $lMyItem->levelTotalExp, $lMyItem->levelExp);
                $returnData->myVsGameInfo = $lMyItem;
            } else {
                $lResult2 = $gConn->query("SELECT icon_url,nickname FROM t_all_gameusers WHERE user_id={$user_id}");
                if ($lResult2) {
                    $lNum2 = $lResult2->num_rows;

                    if ($lNum2 == 1) {
                        $lRow2 = $lResult2->fetch_assoc();

                        $lMyItem = new MyVSGameInfo();
                        $lMyItem->icon_url = $lRow2['icon_url'];
                        $lMyItem->nickname = $lRow2['nickname'];
                        $lMyItem->level = 1;
                        $lMyItem->rank = 0;
                        $lMyItem->rankLevel = 1;
                        $lMyItem->levelExp = 0;
                        $lMyItem->levelTotalExp = 500;
                        $returnData->myVsGameInfo = $lMyItem;
                    }

                    $lResult2->free();
                }


                $lMyItem = new MyVSGameInfo();


            }

            $lResult->free();
        }
    } else if (1 == $gContentType) {
        $lResult = $gConn->query("SELECT ta.user_id,ta.level,ta.points,tb.icon_url,tb.nickname FROM t_hw_user_scores_coop_{$game_id} AS ta INNER JOIN t_all_gameusers AS tb ON ta.user_id=tb.user_id WHERE ta.user_id={$user_id}");
        if ($lResult) {
            $lNum = $lResult->num_rows;

            if ($lNum == 1) {
                $lRow = $lResult->fetch_assoc();

                $lMyItem = new MyCoopGameInfo();
                $lMyItem->icon_url = $lRow['icon_url'];
                $lMyItem->nickname = $lRow['nickname'];
                $lMyItem->level = intval($lRow['level']);
                // $lMyItem->points = intval($lRow['points']);
                $lMyItem->rank = 0;
                $lMyItem->rankLevel = getRankLevelFromLevel($lLevel);
                getGDLevelExpFromPL($lPoints, $lLevel, $lMyItem->levelTotalExp, $lMyItem->levelExp);
                $returnData->myCoopGameInfo = $lMyItem;
            } else {
                $lResult2 = $gConn->query("SELECT icon_url,nickname FROM t_all_gameusers WHERE user_id={$user_id}");
                if ($lResult2) {
                    $lNum2 = $lResult2->num_rows;

                    if ($lNum2 == 1) {
                        $lRow2 = $lResult2->fetch_assoc();

                        $lMyItem = new MyCoopGameInfo();
                        $lMyItem->icon_url = $lRow2['icon_url'];
                        $lMyItem->nickname = $lRow2['nickname'];
                        $lMyItem->level = 1;
                        $lMyItem->rank = 0;
                        $lMyItem->rankLevel = 1;
                        $lMyItem->levelExp = 0;
                        $lMyItem->levelTotalExp = 500;
                        $returnData->myCoopGameInfo = $lMyItem;
                    }

                    $lResult2->free();
                }
            }

            $lResult->free();
        }
    }
}



// if (COUNT($lCurUserIDArray) > 0) {
//     $lPlayerInfoArray = getPlayerInfo($gConn, $lCurUserIDArray, TYPE_PINFO_SUB, true, EXTRA_PINFO_NULL);
//     if (NULL != $lPlayerInfoArray) {
//         foreach ($lPlayerInfoArray as $lCurUserID => $value) {
//             $gUsers[$lCurUserID]->playerInfo = $lPlayerInfoArray[$lCurUserID];
//         }
//     }

//     $response->playerGameInfoList = array_values($gUsers);
// }

$returnData->game_id = $game_id;
$returnData->content_type = $gContentType;
$response->data = $returnData;


header('Content-type: application/json');
echo json_encode($response);

$gConn->close();
