<?php return array(
    'root' => array(
        'name' => '__root__',
        'pretty_version' => 'dev-master',
        'version' => 'dev-master',
        'reference' => '9f72ca2f824f6594fc35ac4517d31734a91e82e1',
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        '__root__' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '9f72ca2f824f6594fc35ac4517d31734a91e82e1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'aliyuncs/oss-sdk-php' => array(
            'pretty_version' => 'v2.7.2',
            'version' => '2.7.2.0',
            'reference' => '483dd0b8bff5d47f0e4ffc99f6077a295c5ccbb5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../aliyuncs/oss-sdk-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'brick/math' => array(
            'pretty_version' => '0.12.1',
            'version' => '0.12.1.0',
            'reference' => 'f510c0a40911935b77b86859eb5223d58d660df1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../brick/math',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ramsey/collection' => array(
            'pretty_version' => '2.0.0',
            'version' => '2.0.0.0',
            'reference' => 'a4b48764bfbb8f3a6a4d1aeb1a35bb5e9ecac4a5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ramsey/collection',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ramsey/uuid' => array(
            'pretty_version' => '4.7.6',
            'version' => '4.7.6.0',
            'reference' => '91039bc1faa45ba123c4328958e620d382ec7088',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ramsey/uuid',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'rhumsaa/uuid' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '4.7.6',
            ),
        ),
        'rmccue/requests' => array(
            'pretty_version' => 'v2.0.10',
            'version' => '2.0.10.0',
            'reference' => 'bcf1ac7fe8c0b2b18c1df6d24694cfc96b44b391',
            'type' => 'library',
            'install_path' => __DIR__ . '/../rmccue/requests',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
