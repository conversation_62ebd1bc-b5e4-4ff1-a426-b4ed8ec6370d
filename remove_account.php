<?php

include(dirname(__FILE__) . "/games_funs.php");
include(dirname(__FILE__) . "/PasswordHash.php");

use P<PERSON>Mailer\PHPMailer\PHPMailer;
use P<PERSON>Mailer\PHPMailer\SMTP;
use P<PERSON>Mailer\PHPMailer\Exception;

require 'PHPMailer/src/PHPMailer.php';
require 'PHPMailer/src/SMTP.php';
require 'PHPMailer/src/Exception.php';


class GLResponse
{

    public $msg = 'ok';
    public $ret = 0;
    public $error_code = ERROR_NOERROR;

    public $player_type = -1; //0->guest, 1->email, other invalid

    public $email_addr = '';
    public $nickname = '';
    public $email_no = '';
    public $left_secs = 0;
};

$response = new GLResponse();

$user_id = $_POST['user_id'];
$product_id = $_POST['device_id'];
$channel = $_POST['channel'];
$lang = $_POST['lang'];
$token = $_POST['token'];
$login_token = $_POST['login_token'];

if (NULL == $user_id || NULL == $token || NULL == $login_token || $lang == NULL || $channel == NULL || NULL == $product_id) {
    output_json_para_error_msg($response, 'args is NULL!');
    exit;
}

$user_id = intval($user_id);
$product_id = trim($product_id);
$login_token = trim($login_token);
$channel = intval($channel);
$lang = intval($lang);
$token = trim($token);

if (sha1($user_id . $product_id . $lang . $channel . $VS_KEY) != $token) {
    output_json_token_error($response);
    exit;
}

//-----------------------------------------------------------

$lAES = new AESSSL($EMAILAES128KEY);

//connect database and query
$gConn = db_connect();

if (!$gConn) {
    output_json_db_error($response);
    exit;
}

$product_id = mysqli_real_escape_string($gConn, $product_id);

if (!is_chip_id_exist($gConn, $response, $product_id)) {
    exit;
}

if (!is_login_token_valid($gConn, $response, $user_id, $login_token)) {
    exit;
}


$email_addr = '';
$lEncEmailAddr = '';

$result = $gConn->query("SELECT user_id,name,nickname,ptype FROM t_all_gameusers WHERE user_id = " . $user_id . "");

if (!$result) {
    output_json_db_error($response);
    exit;
} else {
    $result_num = $result->num_rows;

    if ($result_num == 0) {
        output_json_db_error($response);
        exit;
    }

    //fill response
    $row = $result->fetch_assoc();

    $lEncEmailAddr = $row['name'];
    $email_addr = $lAES->decrypt($lEncEmailAddr);
    $response->nickname = $row['nickname'];
    $response->player_type = intval($row['ptype']);

    $result->free();
}

if (0 != $response->player_type && 1 != $response->player_type) {
    output_json_register_error2($response, -4, 'player type error.');
    exit;
}

$gResult = $gConn->query("SELECT channel FROM t_hw_all_channel WHERE channel={$channel}");
if (!$gResult) {
    output_json_db_error($response);
    exit;
} else {
    $lNum = $gResult->num_rows;

    if ($lNum != 1) {
        output_json_api_error($response, -9, 'channel error!');
        exit;
    }

    $gResult->free();
}


if (0 == $response->player_type) {
    //remove login token ???
    $gConn->query("UPDATE t_hw_users_logintoken SET login_token='empty{$login_token}' WHERE user_id={$user_id}");

    //remove name/nickname ???
    $gConn->query("INSERT INTO t_hw_gameusers_rm_account (user_id, name, nickname)  VALUES ({$user_id},'{$lEncEmailAddr}','{$response->nickname}')");
    $gConn->query("UPDATE t_all_gameusers SET name='Empty{$user_id}',nickname='' WHERE user_id={$user_id}");

    //echo "INSERT INTO t_hw_gameusers_rm_account (user_id, name, nickanme)  VALUES ({$user_id},'{$email_addr}','{$response->nickname}')";
    //echo "UPDATE t_all_gameusers SET name='',nickname='' WHERE user_id={$user_id}";

    //remove game win/lose record ???
    for ($idx = 0; $idx < 150; $idx++) {

        switch ($idx) {
            case 0:
            case 1:
            case 2:
            case 4:
            case 5:
            case 6:
            case 7:
            case 8:
            case 9:
            case 14:
            case 15:
            case 16:
            case 17:
            case 18:
            case 19:
            case 20:
            case 21:
            case 22:
            case 23:
            case 24:
            case 25:
            case 26:
            case 27:
            case 28:
            case 29:
            case 30:
                $result = $gConn->query("SELECT * FROM t_hw_user_scores_game_{$idx} WHERE user_id={$user_id}");
                if ($result) {
                    $result_num = $result->num_rows;
                    if (1 == $result_num) {
                        $row = $result->fetch_assoc();

                        $lWin = intval($row['win']);
                        $lLose = intval($row['lose']);
                        $lDraw = intval($row['draw']);
                        $lEscape = intval($row['escape']);
                        $lPoints = intval($row['points']);
                        $lLevel = intval($row['level']);

                        $gConn->query("INSERT INTO t_hw_gameusers_vs_rm_account (user_id, game_id, win, lose, draw, points, level, escape)  VALUES ({$user_id},{$idx},{$lWin},{$lLose},{$lDraw},{$lPoints},{$lLevel},{$lEscape})");
                        $gConn->query("DELETE FROM t_hw_user_scores_game_{$idx} WHERE user_id={$user_id}");
                    }
                    $result->free();
                }
                break;

            default:
                break;
        }
    }
} else {
    $preg_email = '/^[a-zA-Z0-9]+([-_.][a-zA-Z0-9]+)*@([a-zA-Z0-9]+[-.])+([a-z]{2,5})$/ims';
    if (preg_match($preg_email, $email_addr)) {
    } else {
        output_json_register_error2($response, -5, 'name isn`t a valid Email!');
        exit;
    }

    $response->email_addr = $email_addr;

    $CODEERRORLIMITSECS = 15 * 60;

    $result = $gConn->query("SELECT id,try_num,TIMESTAMPDIFF(SECOND, time, now()) AS secs FROM t_hw_email_verify_no_rm_account WHERE email='{$lEncEmailAddr}' AND user_id={$user_id} ORDER BY id DESC LIMIT 1");
    if (!$result) {
        output_json_db_error($response);
        exit;
    } else {
        $result_num = $result->num_rows;
        if (1 == $result_num) {
            $row = $result->fetch_assoc();
            $lSecs = intval($row['secs']);
            $lTryNum = intval($row['try_num']);
            if ($lSecs < 60) {
                $response->left_secs = 60 - $lSecs;
                output_json_register_error2($response, -6, 'repeat send email less 60 seconds');
                exit;
            }
            if ($lTryNum >= $EMAILCODEERRORCNT) {
                if ($lSecs < $CODEERRORLIMITSECS) {
                    $response->left_secs = intval($CODEERRORLIMITSECS - $lSecs);
                    output_json_register_error2($response, -7, 'send email code limit [less 15*60 seconds]');
                    exit;
                }
            }
        }
    }

    $email_code = random_int(100000, 999999);

    $mail = new PHPMailer(); //建立邮件发送类

    $mail->IsSMTP(); // 使用SMTP方式发送
    $mail->CharSet = 'UTF-8'; // 设置邮件的字符编码
    $mail->Host = "smtp.sg.aliyun.com"; // 您的企业邮箱域名
    $mail->SMTPAuth = true; // 启用SMTP验证功能
    $mail->SMTPSecure = "ssl";
    $mail->Port = "465"; //SMTP端口
    $mail->Username = "<EMAIL>"; // 邮箱用户名(请填写完整的email地址)
    $mail->Password = "wR4&vG0:qM2-hM6}"; // 授权码
    $mail->From = "<EMAIL>"; //邮件发送者email地址
    $mail->FromName = "gmarcade";
    $mail->AddAddress("$email_addr", ""); //收件人地址，格式是AddAddress("收件人email","收件人姓名")
    $mail->AddReplyTo("", "");
    #$mail->AddAttachment("/var/tmp/file.tar.gz"); // 添加附件
    $mail->IsHTML(false); // set email format to HTML //是否使用HTML格式
    $mail->Subject = "Verification Code"; //邮件标题
    $mail->Body = 'Your verification code is ' . $email_code . ' . This will be valid for 15 minutes. Please don`t share this with anyone .'; //邮件内容
    // $mail->AltBody = "This is the body in plain text for non-HTML mail clients"; //附加信息，可以省略
    if (!$mail->Send()) {
        output_json_api_error($response, -8, $mail->ErrorInfo);
        exit;
    }

    $cc = strval(random_int(100000, 999999));
    $email_no = 'eno' . date("YmdHis") . $cc;


    $result = $gConn->query("INSERT INTO  t_hw_email_verify_no_rm_account (user_id, email, email_no, code, state, try_num, channel) VALUES({$user_id}, '{$lEncEmailAddr}', '{$email_no}', '{$email_code}', 0, 0, {$channel} )");
    if (!$result) {
        output_json_db_error($response);
        exit;
    }

    $response->email_no = $email_no;
}

header('Content-type: application/json');
echo json_encode($response);

$gConn->close();
