<?php

include(dirname(__FILE__) . "/games_funs.php");

class GLResponse
{

    public $msg = 'ok';
    public $ret = 0;

    public $nickname = ''; //昵称
    public $icon_url = ''; //头像URL
    public $gold = 0; //剩余金币
    public $ptype = 1; //0->游客,1->正常用户,2->知名玩家,3->机器人    
};

$response = new GLResponse();

$user_id = $_GET['user_id'];
$login_token = $_GET['login_token'];
$product_id = $_GET['device_id'];
$lang = $_GET['lang'];
$channel = $_GET['channel'];
$token = $_GET['token'];

if ($user_id == NULL || $login_token == NULL || $product_id == NULL || $lang == NULL || $channel == NULL || $token == NULL) {
    output_json_para_error($response);
    exit;
}

$user_id = intval($user_id);
$lang = intval($lang);
$channel = intval($channel);
$product_id = trim($product_id);
$login_token = trim($login_token);
$token = trim($token);

if (sha1($user_id . $product_id . $lang . $channel . $VS_KEY) != $token) {
    output_json_token_error($response);
    exit;
}

$language_id = $gDLangTSuffix;
if (isset($gAllLanguage[$lang])) {
    $language_id = $lang;
}

//-----------------------------------------------------------

$gConn = db_connect();

if (!$gConn) {
    output_json_db_error($response);
    exit;
}

$product_id = mysqli_real_escape_string($gConn, $product_id);

if (!is_login_token_valid($gConn, $response, $user_id, $login_token)) {
    exit;
}

$gResult = $gConn->query("SELECT ptype,nickname,icon_url FROM t_all_gameusers WHERE user_id={$user_id}");
if (!$gResult) {
    output_json_db_error($response);
    exit;
} else {
    $result_num = $gResult->num_rows;

    if ($result_num == 1) {
        $row = $gResult->fetch_assoc();

        $response->icon_url = trim($row['icon_url']);
        $response->nickname = $row['nickname'];
        $response->ptype = intval($row['ptype']);
    }

    $gResult->free();
}

$gResult = $gConn->query("SELECT gold FROM t_hw_users_package WHERE user_id={$user_id}");
if (!$gResult) {
    output_json_db_error($response);
    exit;
} else {
    $result_num = $gResult->num_rows;

    if ($result_num == 1) {
        $row = $gResult->fetch_assoc();

        $response->gold = intval($row['gold']);
    }

    $gResult->free();
}

header('Content-type: application/json');
echo json_encode($response);

$gConn->close();
