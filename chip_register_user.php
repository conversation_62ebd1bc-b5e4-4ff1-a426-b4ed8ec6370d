<?php

exit;

include(dirname(__FILE__) . "/games_funs.php");

require_once './log.php';

//初始化日志
$logHandler = new CLogFileHandler("/tmp/hw_chip_reg_cn_" . date('Y-m-d') . '.log');
$log = Log::Init($logHandler, 1);

class GLResponse
{

    public $msg = 'ok';
    public $ret = 0;

    public $user_id = 0; //用户ID

    public $nickname = ''; //昵称
    public $icon_url = ''; //头像URL
    public $gold = 0; //剩余金币
    public $server_region_idx = -1; //大区服务器ID(注册时选定，后期不能更改)
    public $region_id = -1; //区域ID
    public $cur_server_region_idx = -1; //当前大区服务器ID,无异常时server_region_idx与其相同，有异常则不同
    public $login_token = ''; //登录token

    public $is_new = 1;
    public $ptype = 1; //0->游客,1->正常用户,2->知名玩家,3->机器人
};

$response = new GLResponse();

$product_id = $_POST['device_id'];
$region_id = $_POST['region_id'];
$channel = $_POST['channel'];
$token = $_POST['token'];

$dev_cpu = $_POST['dev_cpu'];
$dev_gpu = $_POST['dev_gpu'];
$dev_android = $_POST['dev_android'];
$dev_ram = $_POST['dev_ram'];
$dev_rom = $_POST['dev_rom'];
$dev_hdmi = $_POST['dev_hdmi'];
$dev_screen_w = $_POST['dev_screen_w'];
$dev_screen_h = $_POST['dev_screen_h'];

if (NULL == $token || NULL == $product_id || NULL == $region_id || NULL == $channel || NULL == $dev_cpu || NULL == $dev_gpu || NULL == $dev_android || NULL == $dev_ram || NULL == $dev_rom || NULL == $dev_hdmi || NULL == $dev_screen_w || NULL == $dev_screen_h) {
    output_json_para_error($response);
    exit;
}

$product_id = trim($product_id);
$region_id = intval($region_id);
$channel = intval($channel);

$dev_cpu = trim($dev_cpu);
$dev_gpu = trim($dev_gpu);
$dev_android = trim($dev_android);
$dev_ram = trim($dev_ram);
$dev_rom = trim($dev_rom);
$dev_hdmi = trim($dev_hdmi);
$dev_screen_w = trim($dev_screen_w);
$dev_screen_h = trim($dev_screen_h);

if (sha1($product_id . $region_id . $channel . $VS_KEY) != $token) {
    output_json_token_error($response);
    exit;
}

//connect database and query
$gConn = db_connect();

if (!$gConn) {
    output_json_db_error($response);
    exit;
}

$product_id = mysqli_real_escape_string($gConn, $product_id);

$r_dev_regnum = 0;
$r_dev_cpu = '';
$r_dev_gpu = '';
$r_dev_android = '';
$r_dev_ram = '';
$r_dev_rom = '';
$r_dev_hdmi = '';
$r_dev_screen_w = '';
$r_dev_screen_h = '';

$real_macid = '';

$gResult = $gConn->query("SELECT macid,dev_regnum,dev_cpu,dev_gpu,dev_android,dev_ram,dev_rom,dev_hdmi,dev_screen_w,dev_screen_h FROM t_hw_chip_list WHERE macid_enc='{$product_id}'");

if (!$gResult) {
    output_json_db_error($response);
    exit;
} else {

    $result_num = $gResult->num_rows;

    if ($result_num < 1) {
        Log::DEBUG('CHIP ID ERROR:' . $product_id);
        output_json_api_error($response, -2, 'chip id error!');
        exit;
    } else {
        $row = $gResult->fetch_assoc();

        $real_macid = trim($row['macid']);
        $r_dev_regnum = intval($row['dev_regnum']);
        $r_dev_cpu = $row['dev_cpu'];
        $r_dev_gpu = $row['dev_gpu'];
        $r_dev_android = $row['dev_android'];
        $r_dev_ram = $row['dev_ram'];
        $r_dev_rom = $row['dev_rom'];
        $r_dev_hdmi = $row['dev_hdmi'];
        $r_dev_screen_w = $row['dev_screen_w'];
        $r_dev_screen_h = $row['dev_screen_h'];
    }
    $gResult->free();
}

$lMyIP = get_ip();

$MAXIPREGCNT = 10;
$today = date("Y-m-d");
$result_limit = $gConn->query("SELECT COUNT(*) AS cnt FROM t_all_gameusers WHERE ip='{$lMyIP}' AND macid='{$real_macid}' AND register_date LIKE '{$today}%'");

if (!$result_limit) {
    output_json_db_error($response);
    exit;
} else {
    $result_limit_num = $result_limit->num_rows;

    if ($result_limit_num > 0) {
        $row_limit = $result_limit->fetch_assoc();
        $lCnt = intval($row_limit['cnt']);
        if ($lCnt >= $MAXIPREGCNT) {
            output_json_api_error($response, -3, 'guest account imit value reached in the device.');
            exit;
        }
    }

    $result_limit->free();
}

$region_id = $SERVER_REGION_IDX_CHINA_MAINLAND;
// $gResult = $gConn->query("SELECT region_id FROM t_hw_all_server_region WHERE region_id={$region_id}");
// if (!$gResult) {
//     output_json_db_error($response);
//     exit;
// } else {
//     $lNum = $gResult->num_rows;

//     if ($lNum != 1) {
//         output_json_api_error($response, -5, 'region error!');
//         exit;
//     }

//     $gResult->free();
// }

$gResult = $gConn->query("SELECT channel FROM t_hw_all_channel WHERE channel={$channel}");
if (!$gResult) {
    output_json_db_error($response);
    exit;
} else {
    $lNum = $gResult->num_rows;

    if ($lNum != 1) {
        output_json_api_error($response, -6, 'channel error!');
        exit;
    }

    $gResult->free();
}

if ($lMyIP != '127.0.0.1') {

    $reader = new Reader('./GeoIP2/GeoLite2-City.mmdb');
    $record = $reader->city($lMyIP);

    $lRegIPCode = trim($record->country->isoCode);

    $lRegIPCID = 0;
    $gResult = $gConn->query("SELECT id FROM t_hw_country_list WHERE two_letter='{$lRegIPCode}'");
    if ($gResult) {
        $lNum = $gResult->num_rows;

        if (1 == $lNum) {
            $row = $gResult->fetch_assoc();
            $lRegIPCID = intval($row['id']);
        }
    }
} else {
    $lRegIPCID = 211;
}

$lAES = new AESSSL($EMAILAES128KEY);

$name = $lAES->encrypt('Guest' . rand(1, 1000));
$nickname = 'Player_';
$password = '';
$icon_url = '';

$gResult = $gConn->query("SELECT icon_url FROM t_hw_user_avatar_list WHERE is_show=1 ");
if (!$gResult) {
    output_json_db_error($response);
    exit;
} else {
    $result_num = $gResult->num_rows;

    $rand_icon = mt_rand(0, $result_num - 1);

    for ($i = 0; $i < $result_num; $i++) {
        $row = $gResult->fetch_assoc();
        if ($i == $rand_icon) {
            $icon_url = $SERVER_RES_PREFIX[$SERVER_REGION_CUR_IDX] .  "avatar/" . trim($row['icon_url']);
            break;
        }
    }
}

$gConn->begin_transaction();

if ($r_dev_cpu != $dev_cpu || $r_dev_gpu != $dev_gpu || $r_dev_android != $dev_android || $r_dev_ram != $dev_ram || $r_dev_rom != $dev_rom || $r_dev_hdmi != $dev_hdmi || $r_dev_screen_w != $dev_screen_w || $r_dev_screen_h != $dev_screen_h) {
    if ($r_dev_regnum >= $DEVICE_REG_LIMIT) {
        output_json_api_error($response, -4, 'Maximum number of bindings');
        $gConn->rollback();
        exit;
    } else {
        $REG_CHANNEL_SQL = '';
        if ($r_dev_regnum == 0) {
            $REG_CHANNEL_SQL = ",reg_channel={$channel}";
        }
        $gResult = $gConn->query("UPDATE t_hw_chip_list SET dev_regnum=dev_regnum+1,dev_cpu='{$dev_cpu}',dev_gpu='{$dev_gpu}',dev_android='{$dev_android}',dev_ram='{$dev_ram}',dev_rom='{$dev_rom}',dev_hdmi='{$dev_hdmi}',dev_screen_w='{$dev_screen_w}',dev_screen_h='{$dev_screen_h}',lastregip='{$lMyIP}',last_channel={$channel} $REG_CHANNEL_SQL WHERE macid='{$real_macid}'");
        if (!$gResult) {
            output_json_db_error($response);
            $gConn->rollback();
            exit;
        }
        $gResult = $gConn->query("INSERT INTO t_hw_chip_bind_record (macid,ip,channel,dev_cpu,dev_gpu,dev_android,dev_ram,dev_rom,dev_hdmi,dev_screen_w,dev_screen_h) VALUES ('{$real_macid}','{$lMyIP}',{$channel},'{$dev_cpu}','{$dev_gpu}','{$dev_android}','{$dev_ram}','{$dev_rom}','{$dev_hdmi}','{$dev_screen_w}','{$dev_screen_h}')");
        if (!$gResult) {
            output_json_db_error($response);
            $gConn->rollback();
            exit;
        }
    }
}

$register_date =  date("Y-m-d H:i:s");
$gResult = $gConn->query("INSERT INTO t_all_gameusers (name, nickname, icon_url, register_date, ip, reg_ip_country, reg_region, server_region_idx, reg_channel, macid, ptype) VALUES ('{$name}', '{$nickname}', '{$icon_url}', '{$register_date}', '{$lMyIP}', {$lRegIPCID}, {$region_id}, {$SERVER_REGION_CUR_IDX}, {$channel}, '{$real_macid}', 0)");
if (!$gResult) {
    output_json_db_error($response);
    $gConn->rollback();
    exit;
}

$result = $gConn->query("SELECT user_id,icon_url,reg_region FROM t_all_gameusers WHERE name='{$name}'");
if (!$result) {
    output_json_db_error($response);
    $gConn->rollback();
    exit;
} else {
    $row = $result->fetch_assoc();
    $response->user_id =  intval($row['user_id']);
    $response->icon_url =  $row['icon_url'];
    $response->region_id =  intval($row['reg_region']);
    $simple_user_id = $response->user_id;
    if (isset($SERVER_USER_IDX[$SERVER_REGION_CUR_IDX])) {
        $simple_user_id = $simple_user_id - $SERVER_USER_IDX[$SERVER_REGION_CUR_IDX] + ($SERVER_USER_IDX[$SERVER_REGION_CUR_IDX] / 100000);
    }

    $nickname =  'Player_' . $simple_user_id;
    $name =  $lAES->encrypt('Guest' . $response->user_id);

    $result2 = $gConn->query("UPDATE t_all_gameusers SET name='{$name}',nickname='{$nickname}',ptype=0 WHERE user_id=" . $response->user_id . " ");
    if (!$result2) {
        output_json_db_error($response);
        $gConn->rollback();
        exit;
    }
    $response->nickname = $nickname;
    $result2 = $gConn->query("UPDATE t_hw_chip_list SET user_id={$response->user_id},user_reg_time=now() WHERE macid='{$real_macid}' ");
    if (!$result2) {
        output_json_db_error($response);
        $gConn->rollback();
        exit;
    }
}

// 建立背包
$gResult = $gConn->query("INSERT INTO t_hw_users_package (user_id) VALUES ({$response->user_id})");
if (!$gResult) {
    output_json_db_error($response);
    $gConn->rollback();
    exit;
}

$gConn->commit();

//make extra info
$gConn->query("INSERT INTO t_all_gameusers_extra_info (user_id) VALUES ({$response->user_id}) ");

//new login token
$response->login_token = get_new_login_token($gConn, $response->user_id, $channel);


header('Content-type: application/json');
echo json_encode($response);

$gConn->close();
