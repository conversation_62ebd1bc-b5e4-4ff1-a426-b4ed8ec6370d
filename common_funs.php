<?php

require_once 'vendor/autoload.php';

use Ramsey\Uuid\Uuid;

function get_ip()
{
        $ip='';

        if(getenv("HTTP_CLIENT_IP"))
                $ip = getenv("HTTP_CLIENT_IP");
        else if(getenv("HTTP_X_FORWARDED_FOR"))
        {
                $ip_str = getenv("HTTP_X_FORWARDED_FOR");

                $ip_array = explode(',', $ip_str);
                if($ip_array != NULL)
                {
                    $ip =  $ip_array[0];
                }
		else
		{
		    $ip = "Unknow";
		}
        }
        else if(getenv("REMOTE_ADDR"))
                $ip = getenv("REMOTE_ADDR");
        else 
		$ip = "Unknow";

	return $ip;
}

function get_ip_place($ip)
{
	$ip=file_get_contents("http://fw.qq.com/ipaddress");
	$ip=str_replace('"',' ',$ip);
	$ip2=explode("(",$ip);

	$a=substr($ip2[1],0,-2);
	$b=explode(",",$a);

	return $b;
}

function get_place($queryIP){ 
    $url = 'http://ip.qq.com/cgi-bin/searchip?searchip1='.$queryIP; 
    $ch = curl_init($url); 
    #curl_setopt($ch,CURLOPT_ENCODING ,'gb2312'); 
    curl_setopt($ch,CURLOPT_ENCODING ,'utf-8'); 
    curl_setopt($ch, CURLOPT_TIMEOUT, 10); 
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $result = curl_exec($ch); 
    #$result = mb_convert_encoding($result, "utf-8", "gb2312"); 
    curl_close($ch); 
    preg_match("@<span>(.*)</span></p>@iU",$result,$ipArray); 
    $loc = $ipArray[1]; 
    return $loc; 
} 

function get_extension($file)
{
    return substr(strrchr($file, '.'), 1);
}

function startsWith($haystack, $needle)
{
    return $needle === "" || strpos($haystack, $needle) === 0;
}
function endsWith($haystack, $needle)
{
    return $needle === "" || substr($haystack, -strlen($needle)) === $needle;
}

function isValidEmailAddr($aEmailAddr){
    $lPregEmail = '/^[a-zA-Z0-9]+([-_.][a-zA-Z0-9]+)*@([a-zA-Z0-9]+[-.])+([a-z]{2,5})$/ims';
    if(preg_match($lPregEmail,$aEmailAddr)){
        return TRUE;
    }else{
        return FALSE;
    }
}
function isValidNickname($aNickname){
    $lPreg = '/^[A-Za-z0-9]{4,16}+$/';
    if(preg_match($lPreg,$aNickname)){
        return TRUE;
    }else{
        return FALSE;
    }
}

function isValidNickname_RU($aNickname){
    // 使用 \p{Cyrillic} 来匹配所有西里尔字母，并启用Unicode支持
    $lPreg = '/^[A-Za-z0-9\p{Cyrillic}]{4,16}$/u';
    
    if(preg_match($lPreg, $aNickname)){
        return TRUE;
    } else {
        return FALSE;
    }
}

function isValidYZName($aName){
    $lPreg = '/^[A-Za-z0-9]{7,17}+$/';
    if(preg_match($lPreg,$aName)){
        return TRUE;
    }else{
        return FALSE;
    }
}
function isValidIDStr($aIDStr){
    $lPreg = '/^[0-9-]+$/';
    if(preg_match($lPreg,$aIDStr)){
        return TRUE;
    }else{
        return FALSE;
    }
}
function isValidDigitStr($aIDStr){
    $lPreg = '/^[0-9]+$/';
    if(preg_match($lPreg,$aIDStr)){
        return TRUE;
    }else{
        return FALSE;
    }
}

//计算UTF8字符串字符个数
function utf8_strlen($string = null) {
    if(empty($string)) return 0;
    $string = trim($string);
    // 将字符串分解为单元
    preg_match_all("/./us", $string, $match);
    // 返回单元个数
    return count($match[0]);
}

//------------------------------------------------------------
function getRankLevelFromLevel($lLevel){ 
    global $gGameRankLevel;
    $lRankLevel = 1;
    if($lLevel < 0)
        $lRankLevel = 1;
    else if($lLevel <= gMaxGameLevel){
        $lRankLevel = $gGameRankLevel[$lLevel];
    }else{
        $lRankLevel = gMaxGameRankLevel;
    }
    return $lRankLevel;
}
//------
function getGDLevelExpFromPL($lPoints,$lLevel,&$lLevelTotalExp,&$lLevelExp){ 
    if($lLevel <= 0){//impossible ???
        $lLevelTotalExp = 500;
        $lLevelExp = 0;
    }
    else if($lLevel < 5){//4个等级
        $lLevelTotalExp = 500;
        $lLevelExp = intval($lPoints - (500*($lLevel - 1)));
    }
    else if($lLevel < 13){//8个等级
        $lLevelTotalExp = 1000;
        $lLevelExp = intval($lPoints - 2000 - (1000*($lLevel - 5)));
    }
    else if($lLevel < 35){//22个等级
        $lLevelTotalExp = 3000;
        $lLevelExp = intval($lPoints - 10000 - (3000*($lLevel - 13)));
    }else if($lLevel == 35){
        $lLevelTotalExp = 30000;     //3000*10;
        $lLevelExp = intval($lPoints - 76000);
    }else if($lLevel == 36){
        $lLevelTotalExp = 300000;    //3000*10*10;
        $lLevelExp = intval($lPoints - 76000 - 30000);
    }else if($lLevel == 37){
        $lLevelTotalExp = 3000000;   //3000*10*10*10;
        $lLevelExp = intval($lPoints - 76000 - 330000);
    }else if($lLevel == 38){
        $lLevelTotalExp = 30000000;  //3000*10*10*10*10;
        $lLevelExp = intval($lPoints - 76000 - 3330000);
    }else if($lLevel == 39){
        $lLevelTotalExp = 300000000; //3000*10*10*10*10*10;
        $lLevelExp = intval($lPoints - 76000 - 33330000);
    }else{ //$lLevel >= 40
        $lLevelTotalExp = 300000000; //3000*10*10*10*10*10;
        $lLevelExp = 300000000;
    }
}
function getGDLevelFromExp($aExp){ 
    $lLevel = 0;
    if($aExp <= 0)
        $lLevel = 1;
    else if($aExp < 2000)
        $lLevel = floor($aExp/500) + 1;
    else if($aExp < 10000)
        $lLevel = floor(($aExp-2000)/1000) + 5;
    else if($aExp < 76000)
        $lLevel = floor(($aExp-10000)/3000) + 13;
    else if($aExp < (76000 + 30000))
        $lLevel = 35;
    else if($aExp < (76000 + 330000))
        $lLevel = 36;
    else if($aExp < (76000 + 3330000))
        $lLevel = 37;
    else if($aExp < (76000 + 33330000))
        $lLevel = 38;
    else if($aExp < (76000 + 333330000))
        $lLevel = 39;
    else
        $lLevel = 40;

    return $lLevel;
}
function getGDMinLevelExpFromLevel($aLevel){
    $lMinLevelExp = 0;
    if($aLevel <= 0){
        $lMinLevelExp = 0;
    }else if($aLevel < 5){
       $lMinLevelExp = ($aLevel-1)*500; 
    }else if($aLevel < 13){
       $lMinLevelExp = ($aLevel-5)*1000 + 2000; 
    }else if($aLevel < 35){
       $lMinLevelExp = ($aLevel-13)*3000 + 10000; 
    }else if($aLevel == 35){
       $lMinLevelExp = 76000; 
    }else if($aLevel == 36){
       $lMinLevelExp = 76000 + 30000; 
    }else if($aLevel == 37){
       $lMinLevelExp = 76000 + 330000; 
    }else if($aLevel == 38){
       $lMinLevelExp = 76000 + 3330000; 
    }else if($aLevel == 39){
       $lMinLevelExp = 76000 + 33330000; 
    }else{ //>= 40
       $lMinLevelExp = 76000 + 333330000; 
    }

    return $lMinLevelExp;
}
//------
function getGGLevelExpFromPL($lPoints,$lLevel,&$lLevelTotalExp,&$lLevelExp){ 
    if($lLevel <= 0){//impossible ???
        $lLevelTotalExp = 300;
        $lLevelExp = 0;
    }else if($lLevel < 5){
        $lLevelTotalExp = 300;
        $lLevelExp = intval($lPoints - (300*($lLevel - 1)));
    }else if($lLevel < 9){
        $lLevelTotalExp = 600;
        $lLevelExp = intval($lPoints - 1200 - (600*($lLevel - 5)));
    }else if($lLevel < 13){
        $lLevelTotalExp = 1000;
        $lLevelExp = intval($lPoints - 3600 - (1000*($lLevel - 9)));
    }else if($lLevel < 17){
        $lLevelTotalExp = 2000;
        $lLevelExp = intval($lPoints - 7600 - (2000*($lLevel - 13)));
    }else if($lLevel < 21){
        $lLevelTotalExp = 3000;
        $lLevelExp = intval($lPoints - 15600 - (3000*($lLevel - 17)));
    }else if($lLevel < 35){
        $lLevelTotalExp = 5000;
        $lLevelExp = intval($lPoints - 27600 - (5000*($lLevel - 21)));
    }else if($lLevel == 35){
        $lLevelTotalExp = 50000;     //5000*10;
        $lLevelExp = intval($lPoints - 97600);
    }else if($lLevel == 36){
        $lLevelTotalExp = 500000;    //5000*10*10;
        $lLevelExp = intval($lPoints - 97600 - 50000);
    }else if($lLevel == 37){
        $lLevelTotalExp = 5000000;   //5000*10*10*10;
        $lLevelExp = intval($lPoints - 97600 - 550000);
    }else if($lLevel == 38){
        $lLevelTotalExp = 50000000;  //5000*10*10*10*10;
        $lLevelExp = intval($lPoints - 97600 - 5550000);
    }else if($lLevel == 39){
        $lLevelTotalExp = 500000000; //5000*10*10*10*10*10;
        $lLevelExp = intval($lPoints - 97600 - 5550000);
    }else{ //$lLevel >= 40
        $lLevelTotalExp = 500000000; //5000*10*10*10*10*10;
        $lLevelExp = 500000000;
    }
}
function getGGLevelFromExp($aExp){ 
    $lLevel = 0;
    if($aExp <= 0)
        $lLevel = 1;
    else if($aExp < 1200)
        $lLevel = floor($aExp/300) + 1;
    else if($aExp < 3600)
        $lLevel = floor(($aExp-1200)/600) + 5;
    else if($aExp < 7600)
        $lLevel = floor(($aExp-3600)/1000) + 9;
    else if($aExp < 15600)
        $lLevel = floor(($aExp-7600)/2000) + 13;
    else if($aExp < 27600)
        $lLevel = floor(($aExp-15600)/3000) + 17;
    else if($aExp < 97600)
        $lLevel = floor(($aExp-27600)/5000) + 21;
    else if($aExp < (96000 + 50000))
        $lLevel = 35;
    else if($aExp < (96000 + 550000))
        $lLevel = 36;
    else if($aExp < (96000 + 5550000))
        $lLevel = 37;
    else if($aExp < (96000 + 55550000))
        $lLevel = 38;
    else if($aExp < (96000 + 555550000))
        $lLevel = 39;
    else
        $lLevel = 40;

    return $lLevel;
}

//------


/**
 * 生成唯一的盐值(salt)
 */
function generateUniqueSalt()
{
    return Uuid::uuid4()->toString();
}


function generateEncryptedSalt($hardwareFingerprint, $fingerSalt)
{

    // 结合硬件指纹和salt
    $combinedString = $hardwareFingerprint . $fingerSalt;

    // 使用强密码哈希算法(如bcrypt)来加密组合字符串
    $encryptedSalt = password_hash($combinedString, PASSWORD_BCRYPT);

    return $encryptedSalt;
}

function verifyEncryptedSalt($hardwareFingerprint, $originalSalt, $storedEncryptedSalt)
{
    $combinedString = $hardwareFingerprint . $originalSalt;
    return password_verify($combinedString, $storedEncryptedSalt);
}

?>
