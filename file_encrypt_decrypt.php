<?php

require_once 'maesssl.php';

class FileEncryptDecrypt {
    private $aes;
    
    /**
     * 构造函数
     *
     * @param string $key 密钥
     * @param string $method 加密方式，默认 AES-128-ECB
     * @param string $iv iv向量
     * @param int $options 选项参数
     */
    public function __construct($key, $method = 'AES-128-ECB', $iv = '', $options = 0) {
        $this->aes = new AESSSL($key, $method, $iv, $options);
    }
    
    /**
     * 加密文件
     *
     * @param string $inputFile 输入文件路径
     * @param string $outputFile 输出文件路径
     * @return bool 成功返回true，失败返回false
     */
    public function encryptFile($inputFile, $outputFile) {
        try {
            // 检查输入文件是否存在
            if (!file_exists($inputFile)) {
                throw new Exception("输入文件不存在: $inputFile");
            }
            
            // 读取文件内容
            $data = file_get_contents($inputFile);
            if ($data === false) {
                throw new Exception("无法读取文件: $inputFile");
            }
            
            // 加密数据
            $encryptedData = $this->aes->encrypt($data);
            if ($encryptedData === false) {
                throw new Exception("加密失败");
            }
            
            // 写入加密后的数据到输出文件
            $result = file_put_contents($outputFile, $encryptedData);
            if ($result === false) {
                throw new Exception("无法写入文件: $outputFile");
            }
            
            echo "文件加密成功！\n";
            echo "输入文件: $inputFile\n";
            echo "输出文件: $outputFile\n";
            echo "文件大小: " . strlen($data) . " bytes -> " . strlen($encryptedData) . " bytes\n";
            
            return true;
            
        } catch (Exception $e) {
            echo "加密失败: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    /**
     * 解密文件
     *
     * @param string $inputFile 输入文件路径（加密文件）
     * @param string $outputFile 输出文件路径（解密文件）
     * @return bool 成功返回true，失败返回false
     */
    public function decryptFile($inputFile, $outputFile) {
        try {
            // 检查输入文件是否存在
            if (!file_exists($inputFile)) {
                throw new Exception("输入文件不存在: $inputFile");
            }
            
            // 读取加密文件内容
            $encryptedData = file_get_contents($inputFile);
            if ($encryptedData === false) {
                throw new Exception("无法读取文件: $inputFile");
            }
            
            // 解密数据
            $decryptedData = $this->aes->decrypt($encryptedData);
            if ($decryptedData === false) {
                throw new Exception("解密失败，可能是密钥错误或文件已损坏");
            }
            
            // 写入解密后的数据到输出文件
            $result = file_put_contents($outputFile, $decryptedData);
            if ($result === false) {
                throw new Exception("无法写入文件: $outputFile");
            }
            
            echo "文件解密成功！\n";
            echo "输入文件: $inputFile\n";
            echo "输出文件: $outputFile\n";
            echo "文件大小: " . strlen($encryptedData) . " bytes -> " . strlen($decryptedData) . " bytes\n";
            
            return true;
            
        } catch (Exception $e) {
            echo "解密失败: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    /**
     * 批量加密文件夹中的文件
     *
     * @param string $inputDir 输入文件夹路径
     * @param string $outputDir 输出文件夹路径
     * @param string $extension 文件扩展名过滤（可选）
     * @return int 成功加密的文件数量
     */
    public function encryptDirectory($inputDir, $outputDir, $extension = '') {
        $successCount = 0;
        
        // 确保输出目录存在
        if (!is_dir($outputDir)) {
            mkdir($outputDir, 0755, true);
        }
        
        // 扫描输入目录
        $files = scandir($inputDir);
        
        foreach ($files as $file) {
            if ($file == '.' || $file == '..') {
                continue;
            }
            
            $inputFile = $inputDir . DIRECTORY_SEPARATOR . $file;
            
            // 跳过目录
            if (is_dir($inputFile)) {
                continue;
            }
            
            // 如果指定了扩展名，则进行过滤
            if (!empty($extension) && pathinfo($file, PATHINFO_EXTENSION) !== $extension) {
                continue;
            }
            
            $outputFile = $outputDir . DIRECTORY_SEPARATOR . $file . '.encrypted';
            
            if ($this->encryptFile($inputFile, $outputFile)) {
                $successCount++;
            }
        }
        
        echo "批量加密完成，成功加密 $successCount 个文件\n";
        return $successCount;
    }
    
    /**
     * 批量解密文件夹中的文件
     *
     * @param string $inputDir 输入文件夹路径
     * @param string $outputDir 输出文件夹路径
     * @return int 成功解密的文件数量
     */
    public function decryptDirectory($inputDir, $outputDir) {
        $successCount = 0;
        
        // 确保输出目录存在
        if (!is_dir($outputDir)) {
            mkdir($outputDir, 0755, true);
        }
        
        // 扫描输入目录
        $files = scandir($inputDir);
        
        foreach ($files as $file) {
            if ($file == '.' || $file == '..') {
                continue;
            }
            
            $inputFile = $inputDir . DIRECTORY_SEPARATOR . $file;
            
            // 跳过目录
            if (is_dir($inputFile)) {
                continue;
            }
            
            // 只处理 .encrypted 文件
            if (pathinfo($file, PATHINFO_EXTENSION) !== 'encrypted') {
                continue;
            }
            
            // 移除 .encrypted 扩展名
            $originalName = pathinfo($file, PATHINFO_FILENAME);
            $outputFile = $outputDir . DIRECTORY_SEPARATOR . $originalName;
            
            if ($this->decryptFile($inputFile, $outputFile)) {
                $successCount++;
            }
        }
        
        echo "批量解密完成，成功解密 $successCount 个文件\n";
        return $successCount;
    }
}

// 命令行使用示例
// if (php_sapi_name() === 'cli') {
//     echo "文件加解密工具\n";
//     echo "================\n";
    
//     // 检查参数
//     if ($argc < 2) {
//         echo "使用方法:\n";
//         echo "  加密单个文件: php file_encrypt_decrypt.php encrypt <密钥> <输入文件> <输出文件> [加密方式] [IV向量]\n";
//         echo "  解密单个文件: php file_encrypt_decrypt.php decrypt <密钥> <输入文件> <输出文件> [加密方式] [IV向量]\n";
//         echo "  批量加密目录: php file_encrypt_decrypt.php encrypt-dir <密钥> <输入目录> <输出目录> [文件扩展名] [加密方式] [IV向量]\n";
//         echo "  批量解密目录: php file_encrypt_decrypt.php decrypt-dir <密钥> <输入目录> <输出目录> [加密方式] [IV向量]\n";
//         echo "\n示例:\n";
//         echo "  php file_encrypt_decrypt.php encrypt mykey123 test.txt test.txt.encrypted\n";
//         echo "  php file_encrypt_decrypt.php decrypt mykey123 test.txt.encrypted test_decrypted.txt\n";
//         echo "  php file_encrypt_decrypt.php encrypt-dir mykey123 ./input ./output txt\n";
//         exit(1);
//     }
    
//     $action = $argv[1];
//     $key = $argv[2] ?? '';
//     $input = $argv[3] ?? '';
//     $output = $argv[4] ?? '';
//     $method = $argv[5] ?? 'AES-128-ECB';
//     $iv = $argv[6] ?? '';
    
//     if (empty($key)) {
//         echo "错误: 必须提供密钥\n";
//         exit(1);
//     }
    
//     $fileEncrypt = new FileEncryptDecrypt($key, $method, $iv);
    
//     switch ($action) {
//         case 'encrypt':
//             if (empty($input) || empty($output)) {
//                 echo "错误: 必须提供输入文件和输出文件路径\n";
//                 exit(1);
//             }
//             $fileEncrypt->encryptFile($input, $output);
//             break;
            
//         case 'decrypt':
//             if (empty($input) || empty($output)) {
//                 echo "错误: 必须提供输入文件和输出文件路径\n";
//                 exit(1);
//             }
//             $fileEncrypt->decryptFile($input, $output);
//             break;
            
//         case 'encrypt-dir':
//             if (empty($input) || empty($output)) {
//                 echo "错误: 必须提供输入目录和输出目录路径\n";
//                 exit(1);
//             }
//             $extension = $argv[5] ?? '';
//             $method = $argv[6] ?? 'AES-128-ECB';
//             $iv = $argv[7] ?? '';
//             $fileEncrypt = new FileEncryptDecrypt($key, $method, $iv);
//             $fileEncrypt->encryptDirectory($input, $output, $extension);
//             break;
            
//         case 'decrypt-dir':
//             if (empty($input) || empty($output)) {
//                 echo "错误: 必须提供输入目录和输出目录路径\n";
//                 exit(1);
//             }
//             $fileEncrypt->decryptDirectory($input, $output);
//             break;
            
//         default:
//             echo "错误: 未知操作 '$action'\n";
//             echo "支持的操作: encrypt, decrypt, encrypt-dir, decrypt-dir\n";
//             exit(1);
//     }
// }

?>
