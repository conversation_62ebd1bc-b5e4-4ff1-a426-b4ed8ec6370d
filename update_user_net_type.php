<?php

include (dirname(__FILE__)."/games_funs.php");

class GLResponse{

    public $msg = 'ok';
    public $ret = 0;

};

$response = new GLResponse();

//args
$user_id = $_POST['user_id'];
$login_token = $_POST['login_token'];
$product_id = $_POST['device_id'];

$net_type = $_POST['net_type'];

$token = $_POST['token'];

/*
$user_id = 1000;
$login_token = 'e33ae3984260e1b1a8dfbf8af86f509c0a3b162e';
$product_id = 'aaa';

$net_type = 5;

$token = 'aaaaa';
 */

if(NULL === $user_id
        || NULL === $login_token 
        || NULL === $product_id
        || NULL === $token
        || NULL === $net_type
  ){
    output_json_para_error($response);
    exit;
}else{
    
    $user_id = intval($user_id);
    $login_token = trim($login_token);
    $product_id = trim($product_id);
    
    $token = trim($token);

    $net_type = intval($net_type);

    if($user_id <= 0
            || $LOGINTOKENLEN != strlen($login_token)
            || 0 == strlen($token)
            || !in_array($net_type,NETTYPEARRAY)
      ){
        output_json_para_error($response);
        exit;
    }
}

if(sha1($user_id.$product_id.$net_type.$VS_KEY) != $token)
{
    output_json_token_error($response);
    exit;
}

$gConn = db_connect();

if(!$gConn)
{
    output_json_db_error($response);
    exit;
}

$product_id = mysqli_real_escape_string($gConn, $product_id);
if (!is_chip_id_exist($gConn, $response, $product_id)) {
    exit;
}

if(!is_login_token_valid($gConn,$response,$user_id,$login_token)){ exit; }

$gResult = $gConn->query("UPDATE t_all_gameusers_extra_info SET net_type={$net_type}  WHERE user_id = {$user_id}");
if(!$gResult){
    output_json_db_error_with_msg($response,mysqli_error($gConn));
    exit();
}

header('Content-type: application/json');
echo json_encode($response);

$gConn->close();

?>
