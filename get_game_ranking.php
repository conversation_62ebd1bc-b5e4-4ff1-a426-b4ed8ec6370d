<?php

include (dirname(__FILE__)."/games_funs.php");

include (dirname(__FILE__)."/player_common_struct.php");

//---------------- JSON DATA CLASS ---------------------------

class GLResponse{

    public $msg = 'ok';
    public $ret = 0;

    public $playerGameInfoList = NULL;    
};

class PlayerGameInfo{
    public $playerInfo = NULL;
    public $vsGameInfo = NULL;
    public $coopGameInfo = NULL;
}

class VSGameInfo{

    public $level = 1;
    public $rankLevel = 1;
    public $levelExp = 0;
    public $levelTotalExp = 500;

    public $win = 0;
    public $lose = 0;
    public $draw = 0;
    public $escape = 0;
}

class CooperativeGameInfo{

    public $level = 1;
    public $rankLevel = 1;
    public $levelExp = 0;
    public $levelTotalExp = 500;

    public $single_tg_num = 0;
    public $multi_tg_num = 0;
}

//-----------------------------------------------------------

$response = new GLResponse();

//args
$user_id = $_GET['user_id'];
$login_token = $_GET['login_token'];
$product_id = $_GET['device_id'];

$game_id = $_GET['game_id'];

$token = $_GET['token'];

/*
$user_id = 8;
$login_token = 'e33ae3984260e1b1a8dfbf8af86f509c0a3b162e';
$product_id = 'aaa';

$game_id = 0;

$token = 'aaaaa';
 */

if(NULL === $user_id
    || NULL === $login_token 
    || NULL === $product_id
    || NULL === $game_id
    || NULL === $token
){
    output_json_para_error($response);
    exit;
}else{

    $user_id = intval($user_id);
    $login_token = trim($login_token);
    $product_id = trim($product_id);
    
    $game_id = intval($game_id);

    $token = trim($token);

    if($user_id <= 0
        || $LOGINTOKENLEN != strlen($login_token)
        || 0 == strlen($token)
    ){
        output_json_para_error($response);
        exit;
    }
}

if (sha1($user_id.$product_id.$game_id.$VS_KEY) != $token)
{
    output_json_token_error($response);
    exit;
}

$gConn = db_connect();

if(!$gConn)
{
    output_json_db_error($response);
    exit;
}

$product_id = mysqli_real_escape_string($gConn, $product_id);
if (!is_chip_id_exist($gConn, $response, $product_id)) {
    exit;
}

if(!is_login_token_valid($gConn,$response,$user_id,$login_token)){ exit; }

$gContentType = 0;
$lResult = $gConn->query("SELECT content_type FROM t_hw_all_games_common WHERE game_id={$game_id} AND has_records=1");
if (!$lResult) 
{
    output_json_db_error_with_msg($response,mysqli_error($gConn));
    exit;
}else{
    $lNum = $lResult->num_rows;
    
    if(1 != $lNum){
        output_json_db_error_with_msg($response,"Game id error");
        exit;
    }else{
        $lRow = $lResult->fetch_assoc();
        $gContentType = intval($lRow['content_type']);
    }
}

//--------------------------------------------------------------------------------

$MAXRANKNUM = 100; 

$gUsers = [];
$lCurUserIDArray = [];
if(0 == $gContentType){
    $lResult = $gConn->query("SELECT user_id,win,lose,draw,escape,level,points FROM t_hw_user_scores_game_{$game_id} ORDER BY points DESC LIMIT {$MAXRANKNUM}");

    if (!$lResult) 
    {
        output_json_db_error_with_msg($response,mysqli_error($gConn));
        exit;
    }
    else
    {
        $lNum = $lResult->num_rows;

        for($lIDX=0;$lIDX<$lNum;$lIDX++){
            //fill response
            $lRow = $lResult->fetch_assoc();

            $lCurUserID = intval($lRow['user_id']);

            $gUsers[$lCurUserID] = new PlayerGameInfo();
            $gUsers[$lCurUserID]->vsGameInfo = new VSGameInfo();

            $gUsers[$lCurUserID]->vsGameInfo->win = intval($lRow['win']);
            $gUsers[$lCurUserID]->vsGameInfo->lose = intval($lRow['lose']);
            $gUsers[$lCurUserID]->vsGameInfo->draw = intval($lRow['draw']);
            $gUsers[$lCurUserID]->vsGameInfo->escape = intval($lRow['escape']);

            $lLevel = intval($lRow['level']);
            $lPoints = intval($lRow['points']);

            //------------------------------------------------------------
            $gUsers[$lCurUserID]->vsGameInfo->level = $lLevel;
            $gUsers[$lCurUserID]->vsGameInfo->rankLevel = getRankLevelFromLevel($lLevel);
            getGDLevelExpFromPL($lPoints,$lLevel,$gUsers[$lCurUserID]->vsGameInfo->levelTotalExp,$gUsers[$lCurUserID]->vsGameInfo->levelExp);
            
            $lCurUserIDArray[] = $lCurUserID;
        }

        $lResult->free();
    }
}else{
    //TODO GG
}

if(COUNT($lCurUserIDArray) > 0){
    $lPlayerInfoArray = getPlayerInfo($gConn,$lCurUserIDArray,TYPE_PINFO_SUB,true,EXTRA_PINFO_NULL);
    if(NULL != $lPlayerInfoArray){
        foreach($lPlayerInfoArray as $lCurUserID => $value){
            $gUsers[$lCurUserID]->playerInfo = $lPlayerInfoArray[$lCurUserID]; 
        }
    }

    $response->playerGameInfoList = array_values($gUsers);
}


header('Content-type: application/json');
echo json_encode($response);

$gConn->close();

?>
